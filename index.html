<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة الشامل</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
        }
        .card-gradient {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%);
        }
        .sidebar-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-radius: 8px;
            transition: all 0.2s;
            cursor: pointer;
        }
        .sidebar-item:hover {
            background-color: #f3f4f6;
        }
        .sidebar-item.active {
            background-color: #3b82f6;
            color: white;
        }
        .dark {
            background-color: #1f2937 !important;
            color: #f9fafb !important;
        }
        .dark .card-gradient {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%) !important;
        }
        .dark .bg-white {
            background-color: #374151 !important;
            color: #f9fafb !important;
        }
        .dark .bg-gray-50 {
            background-color: #4b5563 !important;
        }
        .dark .border-gray-200 {
            border-color: #6b7280 !important;
        }
        .dark .text-gray-600 {
            color: #d1d5db !important;
        }
        .dark .text-gray-500 {
            color: #9ca3af !important;
        }
        .dark .hover\\:bg-gray-50:hover {
            background-color: #4b5563 !important;
        }
        .dark .hover\\:bg-gray-100:hover {
            background-color: #4b5563 !important;
        }

        /* تحسين الأزرار */
        .btn {
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        /* تحسين الجداول مع تأثير التمرير الموحد */
        .table-row {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .table-row:hover {
            background-color: #f8fafc !important;
            transform: scale(1.02);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            z-index: 10;
            position: relative;
        }

        .dark .table-row:hover {
            background-color: #4b5563 !important;
            box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
        }

        /* تأثير التمرير للبطاقات العامة */
        .card-hover {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .card-hover:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #3b82f6;
        }

        .dark .card-hover:hover {
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
        }

        /* تأثير مخصص لبطاقات الموردين - فقط تغيير لون الإطار */
        .supplier-card {
            transition: border-color 0.3s ease;
            cursor: pointer;
        }

        .supplier-card:hover {
            border-color: #3b82f6 !important;
        }

        .dark .supplier-card:hover {
            border-color: #60a5fa !important;
        }

        /* منع السهم الأفقي وتحسين تنسيق الجداول */
        .table-container {
            overflow-x: hidden !important;
            width: 100%;
        }

        .table-fixed {
            table-layout: fixed;
            width: 100%;
        }

        .table-fixed th,
        .table-fixed td {
            word-wrap: break-word;
            overflow-wrap: break-word;
            text-align: center;
            vertical-align: middle;
        }

        /* تحسين عرض النصوص في الخلايا */
        .table-fixed td {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* تحسين الحالات */
        .status-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 80px;
        }

        /* تحسين النماذج */
        .form-input {
            transition: all 0.2s ease;
        }
        .form-input:focus {
            transform: scale(1.02);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* تحسين البطاقات */
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        /* تحسين الأيقونات */
        .icon-btn {
            padding: 8px;
            border-radius: 6px;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .icon-btn:hover {
            background-color: #e5e7eb;
            transform: scale(1.1);
        }
        .dark .icon-btn:hover {
            background-color: #4b5563 !important;
        }

        /* إصلاحات إضافية للوضع الليلي */
        .dark .text-gray-900 {
            color: #f9fafb !important;
        }
        .dark .text-gray-800 {
            color: #f3f4f6 !important;
        }
        .dark .text-gray-700 {
            color: #e5e7eb !important;
        }
        .dark .bg-gray-100 {
            background-color: #4b5563 !important;
        }
        .dark .border-gray-100 {
            border-color: #6b7280 !important;
        }
        .dark input, .dark select, .dark textarea {
            background-color: #374151 !important;
            border-color: #6b7280 !important;
            color: #f9fafb !important;
        }
        .dark input:focus, .dark select:focus, .dark textarea:focus {
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 1px #3b82f6 !important;
        }
        .dark .btn {
            color: white !important;
        }
        .dark .bg-gradient-to-r {
            background: linear-gradient(135deg, #374151 0%, #4b5563 100%) !important;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- الشريط الجانبي -->
        <div class="w-64 bg-white border-l border-gray-200 shadow-sm">
            <!-- شعار التطبيق -->
            <div class="flex h-16 items-center justify-center border-b border-gray-200">
                <div class="flex items-center gap-2">
                    <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600 text-white">
                        📊
                    </div>
                    <span class="text-lg font-bold">نظام المحاسبة</span>
                </div>
            </div>

            <!-- قائمة التنقل -->
            <nav class="p-4 space-y-1">
                <div class="sidebar-item active" onclick="showSection('dashboard')">
                    <span>🏠</span>
                    <span class="font-medium">لوحة التحكم</span>
                </div>
                <div class="sidebar-item" onclick="showSection('purchases')">
                    <span>🛒</span>
                    <span class="font-medium">المشتريات</span>
                </div>
                <div class="sidebar-item" onclick="showSection('payments')">
                    <span>💳</span>
                    <span class="font-medium">المدفوعات</span>
                </div>
                <div class="sidebar-item" onclick="showSection('suppliers')">
                    <span>🏢</span>
                    <span class="font-medium">الموردين</span>
                </div>
                <div class="sidebar-item" onclick="showSection('employees')">
                    <span>👥</span>
                    <span class="font-medium">الموظفين</span>
                </div>
                <div class="sidebar-item" onclick="showSection('expenses')">
                    <span>💰</span>
                    <span class="font-medium">المصروفات اليومية</span>
                </div>
                <div class="sidebar-item" onclick="showSection('sales')">
                    <span>📈</span>
                    <span class="font-medium">إدخال المبيعات</span>
                </div>
                <div class="sidebar-item" onclick="showSection('reports')">
                    <span>📊</span>
                    <span class="font-medium">التقارير</span>
                </div>
                <div class="sidebar-item" onclick="showSection('settings')">
                    <span>⚙️</span>
                    <span class="font-medium">الإعدادات</span>
                </div>
            </nav>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- الهيدر -->
            <header class="flex h-16 items-center justify-between border-b border-gray-200 bg-white px-6">
                <div class="flex items-center gap-4">
                    <div class="relative">
                        <input
                            type="text"
                            placeholder="البحث في النظام..."
                            class="h-10 w-80 rounded-lg border border-gray-300 bg-white pr-10 pl-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                        <span class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400">🔍</span>
                    </div>
                </div>

                <div class="flex items-center gap-2">
                    <button class="p-2 rounded-lg hover:bg-gray-100" onclick="toggleTheme()">
                        <span id="theme-icon">🌙</span>
                    </button>
                    <button class="p-2 rounded-lg hover:bg-gray-100 relative">
                        <span>🔔</span>
                        <span class="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-xs text-white">3</span>
                    </button>
                    <div class="mr-4 text-left">
                        <p class="text-sm font-medium" id="current-date">اليوم: جاري التحميل...</p>
                        <p class="text-xs text-gray-500" id="hijri-date">التاريخ الهجري: جاري التحميل...</p>
                        <p class="text-xs text-gray-500" id="last-update">آخر تحديث: جاري التحميل...</p>
                    </div>
                </div>
            </header>

            <!-- محتوى الصفحة -->
            <main class="flex-1 overflow-y-auto p-6" id="main-content">
                <!-- سيتم تحميل المحتوى هنا -->
            </main>
        </div>
    </div>

    <script>
        // متغير لتتبع القسم الحالي
        let currentActiveSection = 'dashboard';

        // عرض الأقسام المختلفة - دالة مهمة يجب أن تكون في البداية
        function showSection(section) {
            console.log('🔄 عرض القسم:', section);

            // حفظ القسم الحالي
            currentActiveSection = section;

            const content = document.getElementById('main-content');
            if (!content) {
                console.error('❌ لم يتم العثور على عنصر main-content');
                return;
            }

            // إزالة الفئة النشطة من جميع العناصر
            document.querySelectorAll('.sidebar-item').forEach(item => {
                item.classList.remove('active');
            });

            // إضافة الفئة النشطة للعنصر المحدد
            try {
                if (window.event && window.event.target && typeof window.event.target.closest === 'function') {
                    const sidebarItem = window.event.target.closest('.sidebar-item');
                    if (sidebarItem) {
                        sidebarItem.classList.add('active');
                    }
                } else {
                    // البحث عن العنصر المناسب بناءً على اسم القسم
                    const sectionNames = {
                        'dashboard': 'لوحة التحكم',
                        'purchases': 'المشتريات',
                        'payments': 'المدفوعات',
                        'suppliers': 'الموردين',
                        'employees': 'الموظفين',
                        'expenses': 'المصروفات اليومية',
                        'sales': 'إدخال المبيعات',
                        'reports': 'التقارير',
                        'settings': 'الإعدادات'
                    };

                    const sectionName = sectionNames[section];
                    if (sectionName) {
                        const sidebarItems = document.querySelectorAll('.sidebar-item');
                        sidebarItems.forEach(item => {
                            if (item.textContent.includes(sectionName)) {
                                item.classList.add('active');
                            }
                        });
                    }
                }
            } catch (error) {
                console.log('تم تجاهل خطأ في تحديد العنصر النشط:', error);
            }

            // عرض المحتوى المناسب
            switch(section) {
                case 'dashboard':
                    content.innerHTML = showDashboard();
                    break;
                case 'purchases':
                    content.innerHTML = showPurchases();
                    break;
                case 'payments':
                    content.innerHTML = showPayments();
                    break;
                case 'suppliers':
                    content.innerHTML = showSuppliers();
                    break;
                case 'employees':
                    content.innerHTML = showEmployees();
                    break;
                case 'expenses':
                    content.innerHTML = showExpenses();
                    break;
                case 'sales':
                    content.innerHTML = showSales();
                    break;
                case 'reports':
                    content.innerHTML = showReports();
                    break;
                case 'settings':
                    content.innerHTML = `
                        <div class="space-y-6">
                            <h1 class="text-3xl font-bold">⚙️ الإعدادات</h1>

                            <!-- إدارة البيانات -->
                            <div class="rounded-lg border bg-white p-6 shadow-sm">
                                <h2 class="text-xl font-bold mb-4 flex items-center gap-2">
                                    💾 إدارة البيانات
                                </h2>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                    <button onclick="exportAllData()" class="bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 flex items-center justify-center gap-2 transition-colors">
                                        📥 تصدير نسخة احتياطية
                                    </button>
                                    <label class="bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 flex items-center justify-center gap-2 transition-colors cursor-pointer">
                                        📤 استيراد البيانات
                                        <input type="file" accept=".json" onchange="importData(event)" class="hidden">
                                    </label>
                                    <button onclick="saveDataToLocalStorage(); showNotification('تم حفظ البيانات! 💾', 'success')" class="bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 flex items-center justify-center gap-2 transition-colors">
                                        💾 حفظ يدوي
                                    </button>
                                    <button onclick="resetAllData()" class="bg-red-600 text-white px-4 py-3 rounded-lg hover:bg-red-700 flex items-center justify-center gap-2 transition-colors">
                                        🗑️ حذف جميع البيانات
                                    </button>
                                </div>
                                <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                                    <h3 class="font-medium text-blue-800 mb-2">📋 معلومات التخزين:</h3>
                                    <div class="text-sm text-blue-700 space-y-1">
                                        <p>• يتم حفظ البيانات تلقائياً عند كل إضافة أو تعديل أو حذف</p>
                                        <p>• البيانات محفوظة في متصفحك ولن تختفي عند إعادة التحميل</p>
                                        <p>• يمكنك تصدير نسخة احتياطية واستيرادها في أي وقت</p>
                                        <p>• استخدم "حذف جميع البيانات" للعودة للبيانات الافتراضية</p>
                                    </div>
                                </div>
                            </div>

                            <!-- إعدادات النظام -->
                            <div class="rounded-lg border bg-white p-6 shadow-sm">
                                <h2 class="text-xl font-bold mb-4 flex items-center gap-2">
                                    🎨 إعدادات النظام
                                </h2>
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <span class="font-medium">الوضع الليلي</span>
                                        <button onclick="toggleTheme()" class="bg-gray-200 dark:bg-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                                            🌙 تبديل الوضع
                                        </button>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="font-medium">اللغة</span>
                                        <select class="border rounded-lg px-3 py-2">
                                            <option value="ar">العربية</option>
                                            <option value="en" disabled>English (قريباً)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات النظام -->
                            <div class="rounded-lg border bg-white p-6 shadow-sm">
                                <h2 class="text-xl font-bold mb-4 flex items-center gap-2">
                                    ℹ️ معلومات النظام
                                </h2>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <p><strong>إصدار النظام:</strong> 1.0.0</p>
                                        <p><strong>تاريخ آخر تحديث:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                                    </div>
                                    <div>
                                        <p><strong>عدد الموردين:</strong> <span id="suppliers-count">${mockData.suppliers.length}</span></p>
                                        <p><strong>عدد المشتريات:</strong> <span id="purchases-count">${mockData.purchases.length}</span></p>
                                        <p><strong>عدد المدفوعات:</strong> <span id="payments-count">${mockData.payments.length}</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;
                default:
                    content.innerHTML = showDashboard();
            }
        }

        // دالة توليد رقم الفاتورة التلقائي لكل مورد مع رمز مخصص
        function generateNextInvoiceNumber(supplierId) {
            console.log('🔢 توليد رقم فاتورة جديد للمورد:', supplierId);

            if (!supplierId) {
                console.log('❌ لم يتم تحديد المورد');
                return '';
            }

            // البحث عن المورد
            const supplier = mockData.suppliers.find(s => s.id == supplierId);
            if (!supplier) {
                console.log('❌ لم يتم العثور على المورد');
                return '';
            }

            // الحصول على آخر رقم فاتورة للمورد (الجزء الرقمي فقط)
            let lastNumber = supplier.lastInvoiceNumber || '000';
            console.log('📋 آخر رقم فاتورة للمورد:', lastNumber);

            // تحويل إلى رقم وزيادة 1
            const nextNumber = parseInt(lastNumber) + 1;

            // تنسيق الرقم بـ 3 خانات مع أصفار بادئة
            const formattedNumber = nextNumber.toString().padStart(3, '0');

            // الحصول على رمز المورد أو إنشاء واحد جديد
            let prefix = supplier.invoicePrefix;
            if (!prefix) {
                prefix = generateSupplierPrefix(supplier.name);
                supplier.invoicePrefix = prefix;
                console.log(`🏷️ تم إنشاء رمز جديد للمورد ${supplier.name}: ${prefix}`);
            }

            // تكوين رقم الفاتورة الكامل: رمز المورد + رقم تسلسلي
            const fullInvoiceNumber = `${prefix}-${formattedNumber}`;

            console.log('✅ رقم الفاتورة الجديد:', fullInvoiceNumber);
            return fullInvoiceNumber;
        }

        // دالة إنشاء رمز مخصص للمورد من اسم الشركة (بالإنجليزية)
        function generateSupplierPrefix(supplierName) {
            // قاموس ترجمة الكلمات العربية للإنجليزية
            const arabicToEnglish = {
                // أنواع الشركات
                'شركة': 'CO',
                'مؤسسة': 'EST',
                'مجموعة': 'GRP',
                'مكتب': 'OFF',
                'دار': 'DAR',
                'بيت': 'HSE',

                // الأنشطة التجارية
                'التوريد': 'SUP',
                'الجودة': 'QUA',
                'التقنية': 'TEC',
                'التجارة': 'TRD',
                'الصناعة': 'IND',
                'الخدمات': 'SRV',
                'الاستثمار': 'INV',
                'التطوير': 'DEV',
                'الإنشاء': 'CON',
                'البناء': 'BLD',
                'النقل': 'TRA',
                'اللوجستيات': 'LOG',
                'التسويق': 'MKT',
                'المبيعات': 'SAL',
                'الإنتاج': 'PRD',
                'التصنيع': 'MFG',
                'الهندسة': 'ENG',
                'الاستشارات': 'CON',
                'التدريب': 'TRN',
                'التعليم': 'EDU',
                'الصحة': 'HLT',
                'الطب': 'MED',
                'الصيدلة': 'PHR',
                'الزراعة': 'AGR',
                'الغذاء': 'FOD',
                'المواد': 'MAT',
                'الكيماويات': 'CHM',
                'البترول': 'OIL',
                'الطاقة': 'ENR',
                'الكهرباء': 'ELE',
                'المياه': 'WAT',
                'البيئة': 'ENV',
                'النظافة': 'CLN',
                'الأمن': 'SEC',
                'الحراسة': 'GRD',
                'التأمين': 'INS',
                'البنوك': 'BNK',
                'المالية': 'FIN',
                'المحاسبة': 'ACC',
                'القانونية': 'LEG',
                'العقارات': 'REA',
                'السياحة': 'TOU',
                'الفنادق': 'HTL',
                'المطاعم': 'RES',
                'الترفيه': 'ENT',
                'الرياضة': 'SPO',
                'الثقافة': 'CUL',
                'الإعلام': 'MED',
                'الطباعة': 'PRT',
                'النشر': 'PUB',
                'التصميم': 'DES',
                'الديكور': 'DEC',
                'الأثاث': 'FUR',
                'المنسوجات': 'TEX',
                'الملابس': 'CLO',
                'الأحذية': 'SHO',
                'المجوهرات': 'JWL',
                'الساعات': 'WAT',
                'الإلكترونيات': 'ELE',
                'الكمبيوتر': 'COM',
                'البرمجيات': 'SOF',
                'الاتصالات': 'TEL',
                'الإنترنت': 'NET',

                // أسماء شائعة
                'الأمل': 'HOP',
                'النور': 'LIG',
                'الفجر': 'DAW',
                'الشروق': 'SUN',
                'النجاح': 'SUC',
                'التميز': 'EXC',
                'الإبداع': 'CRE',
                'الابتكار': 'INN',
                'المستقبل': 'FUT',
                'الحديثة': 'MOD',
                'المتقدمة': 'ADV',
                'الذكية': 'SMA',
                'السريعة': 'FAS',
                'الموثوقة': 'REL',
                'المتميزة': 'EXC',
                'الرائدة': 'LEA',
                'العالمية': 'GLO',
                'المحلية': 'LOC',
                'الوطنية': 'NAT',
                'العربية': 'ARA',
                'السعودية': 'SAU',
                'الخليجية': 'GUL',
                'الشرقية': 'EAS',
                'الغربية': 'WES',
                'الشمالية': 'NOR',
                'الجنوبية': 'SOU',
                'المركزية': 'CEN',

                // مدن
                'الرياض': 'RYD',
                'جدة': 'JED',
                'مكة': 'MEC',
                'المدينة': 'MED',
                'الدمام': 'DAM',
                'الخبر': 'KHO',
                'الطائف': 'TAI',
                'أبها': 'ABH',
                'تبوك': 'TAB',
                'حائل': 'HAI',
                'القصيم': 'QAS',
                'جازان': 'JAZ',
                'نجران': 'NAJ',
                'الباحة': 'BAH',
                'الجوف': 'JOU',
                'عرعر': 'ARA',
                'سكاكا': 'SAK',

                // كلمات إضافية
                'المحدودة': 'LTD',
                'ذات': '',
                'المسؤولية': '',
                'للتجارة': '',
                'التجارية': '',
                'والصناعة': '',
                'والخدمات': '',
                'والاستثمار': '',
                'والتطوير': '',
                'والمقاولات': ''
            };

            // تنظيف اسم الشركة وتقسيمه
            const words = supplierName.trim().split(/\s+/);
            let prefix = '';

            // محاولة ترجمة الكلمات للإنجليزية
            for (let word of words) {
                const cleanWord = word.replace(/[^\u0600-\u06FF\u0750-\u077F]/g, ''); // إزالة الرموز والأرقام

                if (arabicToEnglish[cleanWord]) {
                    const translation = arabicToEnglish[cleanWord];
                    if (translation && translation.length > 0) {
                        prefix += translation;
                        if (prefix.length >= 3) break;
                    }
                }
            }

            // إذا لم نحصل على 3 أحرف، استخدم طريقة بديلة
            if (prefix.length < 3) {
                // أخذ أول حرف من كل كلمة وتحويله لرقم ASCII ثم لحرف إنجليزي
                for (let word of words) {
                    if (word.length > 0 && prefix.length < 3) {
                        const firstChar = word.charAt(0);
                        const charCode = firstChar.charCodeAt(0);
                        // تحويل الحرف العربي لحرف إنجليزي بناءً على الكود
                        const englishChar = String.fromCharCode(65 + (charCode % 26)); // A-Z
                        prefix += englishChar;
                    }
                }
            }

            // التأكد من أن الرمز 3 أحرف بالضبط
            if (prefix.length > 3) {
                prefix = prefix.substring(0, 3);
            } else if (prefix.length < 3) {
                // إضافة أحرف عشوائية إذا لزم الأمر
                const randomChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                while (prefix.length < 3) {
                    prefix += randomChars.charAt(Math.floor(Math.random() * randomChars.length));
                }
            }

            // التأكد من أن الرمز فريد
            prefix = ensureUniquePrefix(prefix);

            console.log(`🏷️ تم إنشاء رمز "${prefix}" من اسم "${supplierName}"`);
            return prefix.toUpperCase();
        }

        // دالة تنظيف وتحويل الأرقام بشكل صحيح
        function parseNumber(value) {
            if (value === null || value === undefined || value === '') {
                return 0;
            }

            // تحويل إلى نص أولاً
            let stringValue = String(value).trim();

            // إزالة الفواصل والمسافات
            stringValue = stringValue.replace(/[,\s]/g, '');

            // إزالة أي نص غير رقمي (مثل "ر.س" أو العملات)
            stringValue = stringValue.replace(/[^\d.-]/g, '');

            // تحويل إلى رقم
            const numericValue = parseFloat(stringValue);

            // التحقق من صحة الرقم
            if (isNaN(numericValue)) {
                console.warn(`⚠️ قيمة غير صحيحة: "${value}" تم تحويلها إلى 0`);
                return 0;
            }

            console.log(`🔢 تحويل "${value}" إلى ${numericValue}`);
            return numericValue;
        }

        // دالة تنسيق الأرقام للعرض
        function formatNumber(value, decimals = 0) {
            const num = parseNumber(value);
            return num.toLocaleString('ar-SA', {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            });
        }

        // دالة التحقق من صحة المبلغ المدخل
        function validateAmount(amount, fieldName = 'المبلغ') {
            const numericAmount = parseNumber(amount);

            if (numericAmount < 0) {
                console.error(`❌ ${fieldName} لا يمكن أن يكون سالباً: ${amount}`);
                return { valid: false, value: 0, message: `${fieldName} لا يمكن أن يكون سالباً` };
            }

            if (numericAmount > 999999999) {
                console.error(`❌ ${fieldName} كبير جداً: ${amount}`);
                return { valid: false, value: 0, message: `${fieldName} كبير جداً (الحد الأقصى 999,999,999)` };
            }

            console.log(`✅ ${fieldName} صحيح: ${numericAmount}`);
            return { valid: true, value: numericAmount, message: '' };
        }

        // دالة للتأكد من تفرد الرمز
        function ensureUniquePrefix(prefix) {
            const existingPrefixes = mockData.suppliers.map(s => s.invoicePrefix).filter(p => p);

            if (!existingPrefixes.includes(prefix)) {
                return prefix;
            }

            // إذا كان الرمز موجود، أضف رقم
            for (let i = 1; i <= 99; i++) {
                const newPrefix = prefix.substring(0, 2) + i.toString().padStart(1, '0');
                if (!existingPrefixes.includes(newPrefix)) {
                    console.log(`⚠️ الرمز ${prefix} موجود مسبقاً، تم تغييره إلى ${newPrefix}`);
                    return newPrefix;
                }
            }

            // إذا فشل كل شيء، استخدم رمز عشوائي
            const randomChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            let randomPrefix = '';
            for (let i = 0; i < 3; i++) {
                randomPrefix += randomChars.charAt(Math.floor(Math.random() * randomChars.length));
            }

            console.log(`⚠️ تم إنشاء رمز عشوائي: ${randomPrefix}`);
            return randomPrefix;
        }

        // دالة تحديث آخر رقم فاتورة للمورد
        function updateSupplierLastInvoiceNumber(supplierId, invoiceNumber) {
            const supplier = mockData.suppliers.find(s => s.id == supplierId);
            if (supplier) {
                // استخراج الجزء الرقمي فقط للحفظ
                const numericPart = extractNumericPart(invoiceNumber);
                supplier.lastInvoiceNumber = numericPart;
                saveDataToLocalStorage();
                console.log(`📝 تم تحديث آخر رقم فاتورة للمورد ${supplier.name}: ${invoiceNumber} (الجزء الرقمي: ${numericPart})`);
            }
        }

        // دالة التحقق من تفرد رقم الفاتورة لكل مورد
        function validateInvoiceNumber(supplierId, invoiceNumber) {
            // التحقق من التفرد على مستوى المورد
            const existingPurchase = mockData.purchases.find(p =>
                p.supplier_id == supplierId && (p.invoice_number === invoiceNumber || p.invoice === invoiceNumber)
            );

            if (existingPurchase) {
                console.log('❌ رقم الفاتورة موجود مسبقاً لهذا المورد');
                return false;
            }

            // التحقق من التفرد على مستوى النظام (اختياري - للتأكد الإضافي)
            const globalExisting = mockData.purchases.find(p =>
                p.invoice === invoiceNumber || p.invoice_number === invoiceNumber
            );

            if (globalExisting) {
                console.log('⚠️ رقم الفاتورة موجود لمورد آخر - سيتم السماح به لأن كل مورد له رمز مختلف');
            }

            console.log('✅ رقم الفاتورة متاح للمورد');
            return true;
        }

        // دالة استخراج الجزء الرقمي من رقم الفاتورة
        function extractNumericPart(invoiceNumber) {
            if (!invoiceNumber) return '000';

            // إذا كان الرقم يحتوي على رمز (مثل SUP-001)
            if (invoiceNumber.includes('-')) {
                return invoiceNumber.split('-')[1] || '000';
            }

            // إذا كان رقم عادي
            return invoiceNumber;
        }

        // دالة حساب الرصيد الحالي للمورد
        function calculateSupplierBalance(supplierId) {
            const supplier = mockData.suppliers.find(s => s.id == supplierId);
            if (!supplier) return 0;

            // الرصيد السابق
            const previousBalance = supplier.previousBalance || 0;

            // إجمالي المشتريات
            const totalPurchases = mockData.purchases
                .filter(p => p.supplier_id == supplierId)
                .reduce((sum, p) => sum + p.amount, 0);

            // إجمالي المدفوعات
            const totalPayments = mockData.payments
                .filter(p => p.supplier_id == supplierId)
                .reduce((sum, p) => sum + p.amount, 0);

            // الرصيد الحالي = الرصيد السابق + إجمالي المشتريات - إجمالي المدفوعات
            const currentBalance = previousBalance + totalPurchases - totalPayments;

            console.log(`💰 حساب رصيد المورد ${supplier.name}:`);
            console.log(`📊 الرصيد السابق: ${previousBalance}`);
            console.log(`📦 إجمالي المشتريات: ${totalPurchases}`);
            console.log(`💳 إجمالي المدفوعات: ${totalPayments}`);
            console.log(`💰 الرصيد الحالي: ${currentBalance}`);

            return currentBalance;
        }

        // دالة تحديث رصيد المورد
        function updateSupplierBalance(supplierId) {
            const supplier = mockData.suppliers.find(s => s.id == supplierId);
            if (supplier) {
                const newBalance = calculateSupplierBalance(supplierId);
                supplier.balance = newBalance;

                // حساب إجمالي المستحقات (المبلغ المطلوب دفعه)
                supplier.totalOutstanding = Math.max(0, newBalance); // فقط الأرصدة الموجبة

                console.log(`✅ تم تحديث رصيد المورد ${supplier.name}: ${newBalance}`);
                return newBalance;
            }
            return 0;
        }

        // دالة تحديث جميع أرصدة الموردين
        function updateAllSuppliersBalances() {
            console.log('🔄 تحديث جميع أرصدة الموردين...');
            mockData.suppliers.forEach(supplier => {
                updateSupplierBalance(supplier.id);
            });
            console.log('✅ تم تحديث جميع أرصدة الموردين');
        }

        // دالة معالجة تغيير المورد في نموذج المشتريات
        function handleSupplierChange(selectElement) {
            const supplierId = selectElement.value;
            const invoiceNumberField = document.getElementById('invoiceNumber');

            if (!invoiceNumberField) {
                console.log('❌ لم يتم العثور على حقل رقم الفاتورة');
                return;
            }

            if (supplierId) {
                // توليد رقم الفاتورة التلقائي
                const nextInvoiceNumber = generateNextInvoiceNumber(supplierId);
                invoiceNumberField.value = nextInvoiceNumber;

                // إضافة معلومات المورد
                const supplier = mockData.suppliers.find(s => s.id == supplierId);
                if (supplier) {
                    console.log(`📋 تم اختيار المورد: ${supplier.name}`);
                    console.log(`🔢 رقم الفاتورة التلقائي: ${nextInvoiceNumber}`);

                    // إظهار رسالة للمستخدم
                    showNotification(`تم توليد رقم الفاتورة ${nextInvoiceNumber} للمورد ${supplier.name}`, 'success');
                }
            } else {
                // مسح رقم الفاتورة إذا لم يتم اختيار مورد
                invoiceNumberField.value = '';
            }
        }

        // دوال التخزين المحلي
        function saveDataToLocalStorage() {
            try {
                const dataToSave = JSON.stringify(mockData);
                localStorage.setItem('accountingSystemData', dataToSave);
                console.log('💾 تم حفظ البيانات في التخزين المحلي');
                console.log('📊 حجم البيانات المحفوظة:', Math.round(dataToSave.length / 1024), 'KB');
                console.log('📈 عدد الموردين:', mockData.suppliers.length);
                console.log('📦 عدد المشتريات:', mockData.purchases.length);
                console.log('💳 عدد المدفوعات:', mockData.payments.length);
                return true;
            } catch (error) {
                console.error('❌ خطأ في حفظ البيانات:', error);
                showNotification('خطأ في حفظ البيانات! ❌', 'error');
                return false;
            }
        }

        function loadDataFromLocalStorage() {
            try {
                const savedData = localStorage.getItem('accountingSystemData');
                if (savedData) {
                    const parsedData = JSON.parse(savedData);
                    // دمج البيانات المحفوظة مع البيانات الافتراضية
                    Object.assign(mockData, parsedData);
                    console.log('📂 تم استرداد البيانات من التخزين المحلي');
                    console.log('📊 حجم البيانات المسترجعة:', Math.round(savedData.length / 1024), 'KB');
                    console.log('📈 عدد الموردين المسترجع:', mockData.suppliers.length);
                    console.log('📦 عدد المشتريات المسترجع:', mockData.purchases.length);
                    console.log('💳 عدد المدفوعات المسترجع:', mockData.payments.length);
                    showNotification('تم استرداد البيانات المحفوظة! 📂', 'success');
                    return true;
                } else {
                    console.log('📝 لا توجد بيانات محفوظة، سيتم استخدام البيانات الافتراضية');
                    showNotification('بدء بالبيانات الافتراضية 📝', 'info');
                    return false;
                }
            } catch (error) {
                console.error('❌ خطأ في استرداد البيانات:', error);
                showNotification('خطأ في استرداد البيانات! ❌', 'error');
                return false;
            }
        }

        function resetAllData() {
            if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                localStorage.removeItem('accountingSystemData');
                location.reload();
            }
        }

        function exportAllData() {
            const dataStr = JSON.stringify(mockData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `نسخة_احتياطية_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
            showNotification('تم تصدير نسخة احتياطية من البيانات! 📥', 'success');
        }

        function importData(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const importedData = JSON.parse(e.target.result);
                        if (confirm('هل تريد استبدال البيانات الحالية بالبيانات المستوردة؟')) {
                            Object.assign(mockData, importedData);
                            saveDataToLocalStorage();
                            showNotification('تم استيراد البيانات بنجاح! 📤', 'success');
                            location.reload();
                        }
                    } catch (error) {
                        showNotification('خطأ في قراءة الملف! ❌', 'error');
                    }
                };
                reader.readAsText(file);
            }
        }

        // بيانات وهمية
        const mockData = {
            kpis: [
                { title: 'إجمالي الإيرادات', value: '125,000 ر.س', change: '+12.5%', icon: '💰' },
                { title: 'إجمالي المبيعات', value: '1,234', change: '+8.2%', icon: '🛒' },
                { title: 'العملاء النشطين', value: '456', change: '+5.1%', icon: '👥' },
                { title: 'المخزون المتاح', value: '2,890', change: '-2.3%', icon: '📦' }
            ],
            recentActivities: [
                { title: 'مبيعة جديدة', description: 'تم بيع 5 قطع من المنتج A', amount: '2,500 ر.س', time: 'منذ 15 دقيقة' },
                { title: 'دفعة للمورد', description: 'تم دفع مستحقات شركة التوريد المحدودة', amount: '15,000 ر.س', time: 'منذ 45 دقيقة' },
                { title: 'تنبيه مخزون', description: 'المنتج B أوشك على النفاد (5 قطع متبقية)', time: 'منذ ساعتان' }
            ],
            purchases: [
                { id: 1, supplier: 'شركة التوريد المحدودة', supplier_id: 1, invoice: 'SUP-001', date: '2024-01-15', amount: 15000, status: 'مستلم', items: 25, description: 'مواد خام للإنتاج' },
                { id: 2, supplier: 'مؤسسة الجودة التجارية', supplier_id: 2, invoice: 'QUA-001', date: '2024-01-14', amount: 8500, status: 'معتمد', items: 12, description: 'قطع غيار ومعدات' },
                { id: 3, supplier: 'شركة التقنية المتقدمة', supplier_id: 3, invoice: 'TEC-001', date: '2024-01-13', amount: 22000, status: 'في الانتظار', items: 8, description: 'أجهزة تقنية متطورة' },
                { id: 4, supplier: 'شركة التوريد المحدودة', supplier_id: 1, invoice: 'SUP-002', date: '2024-01-12', amount: 12000, status: 'مستلم', items: 18, description: 'مواد تشغيلية' },
                { id: 5, supplier: 'مؤسسة الجودة التجارية', supplier_id: 2, invoice: 'QUA-002', date: '2024-01-11', amount: 6500, status: 'معتمد', items: 15, description: 'مستلزمات مكتبية' },
                { id: 6, supplier: 'شركة التوريد المحدودة', supplier_id: 1, invoice: 'SUP-003', date: '2024-01-10', amount: 18500, status: 'مستلم', items: 30, description: 'مواد إضافية' },
                { id: 7, supplier: 'شركة التقنية المتقدمة', supplier_id: 3, invoice: 'TEC-002', date: '2024-01-09', amount: 35000, status: 'معتمد', items: 5, description: 'معدات تقنية جديدة' }
            ],
            suppliers: [
                { id: 1, name: 'شركة التوريد المحدودة', contact: 'أحمد محمد', phone: '+966501234567', email: '<EMAIL>', city: 'الرياض', totalPurchases: 150000, balance: 25000, previousBalance: 18500, lastInvoiceNumber: '003', invoicePrefix: 'SUP' },
                { id: 2, name: 'مؤسسة الجودة التجارية', contact: 'فاطمة أحمد', phone: '+966507654321', email: '<EMAIL>', city: 'جدة', totalPurchases: 85000, balance: 12000, previousBalance: 8200, lastInvoiceNumber: '002', invoicePrefix: 'QUA' },
                { id: 3, name: 'شركة التقنية المتقدمة', contact: 'محمد علي', phone: '+966509876543', email: '<EMAIL>', city: 'الدمام', totalPurchases: 220000, balance: 0, previousBalance: 15000, lastInvoiceNumber: '002', invoicePrefix: 'TEC' }
            ],
            employees: [
                { id: 1, name: 'أحمد محمد علي', position: 'مدير المبيعات', department: 'المبيعات', phone: '+966501234567', email: '<EMAIL>', salary: 12000, status: 'نشط', employeeId: 'EMP-001' },
                { id: 2, name: 'فاطمة أحمد السالم', position: 'محاسبة', department: 'المالية', phone: '+966509876543', email: '<EMAIL>', salary: 9000, status: 'نشط', employeeId: 'EMP-002' },
                { id: 3, name: 'محمد علي الأحمد', position: 'مطور برمجيات', department: 'التقنية', phone: '+966502468135', email: '<EMAIL>', salary: 15000, status: 'في إجازة', employeeId: 'EMP-003' }
            ],
            expenses: [
                { id: 1, description: 'فاتورة الكهرباء - يناير', amount: 2500, date: '2024-01-15', category: 'مرافق', method: 'تحويل بنكي', status: 'معتمد', addedBy: 'أحمد محمد' },
                { id: 2, description: 'وقود السيارات', amount: 800, date: '2024-01-14', category: 'مواصلات', method: 'نقدي', status: 'معتمد', addedBy: 'فاطمة أحمد' },
                { id: 3, description: 'مواد تنظيف المكتب', amount: 350, date: '2024-01-13', category: 'مكتب', method: 'نقدي', status: 'في الانتظار', addedBy: 'محمد علي' },
                { id: 4, description: 'اشتراك الإنترنت', amount: 500, date: '2024-01-12', category: 'تقنية', method: 'تحويل بنكي', status: 'معتمد', addedBy: 'أحمد محمد' }
            ],
            payments: [
                { id: 1, recipient: 'شركة التوريد المحدودة', supplier_id: 1, amount: 15000, date: '2024-01-15', dueDate: '2024-01-20', method: 'تحويل بنكي', status: 'مدفوع', category: 'موردين', type: 'supplier', description: 'دفعة مستحقات شهر ديسمبر', reference: 'TXN-001' },
                { id: 2, recipient: 'مؤسسة الجودة التجارية', supplier_id: 2, amount: 25000, date: '2024-01-14', dueDate: '2024-01-25', method: 'تحويل بنكي', status: 'معلق', category: 'موردين', type: 'supplier', description: 'دفعة أولى لمشروع التوسعة', reference: 'TXN-002' },
                { id: 3, recipient: 'شركة التقنية المتقدمة', supplier_id: 3, amount: 8500, date: '2024-01-13', dueDate: '2024-01-15', method: 'شيك', status: 'مدفوع', category: 'موردين', type: 'supplier', description: 'مستحقات شهر ديسمبر', reference: 'CHK-001' },
                { id: 4, recipient: 'شركة التوريد المحدودة', supplier_id: 1, amount: 12000, date: '2024-01-12', dueDate: '2024-01-22', method: 'تحويل بنكي', status: 'متأخر', category: 'موردين', type: 'supplier', description: 'باقي مستحقات فاتورة سابقة', reference: 'TXN-003' }
            ],
            otherPayments: [
                { id: 1, recipient: 'شركة الكهرباء', amount: 2500, date: '2024-01-15', dueDate: '2024-01-25', method: 'تحويل بنكي', status: 'معلق', category: 'مرافق', type: 'other', description: 'فاتورة الكهرباء - يناير', reference: 'ELEC-001' },
                { id: 2, recipient: 'أحمد محمد - موظف', amount: 8000, date: '2024-01-14', dueDate: '2024-01-15', method: 'نقدي', status: 'متأخر', category: 'رواتب', type: 'other', description: 'راتب شهر ديسمبر' },
                { id: 3, recipient: 'شركة التأمين', amount: 3500, date: '2024-01-13', dueDate: '2024-01-30', method: 'شيك', status: 'مدفوع', category: 'تأمين', type: 'other', description: 'قسط التأمين الشهري', reference: 'INS-001' },
                { id: 4, recipient: 'مكتب المحاماة', amount: 5000, date: '2024-01-12', dueDate: '2024-01-28', method: 'تحويل بنكي', status: 'معلق', category: 'خدمات قانونية', type: 'other', description: 'استشارات قانونية', reference: 'LAW-001' },
                { id: 5, recipient: 'شركة التعاقدات الهندسية', amount: 12000, date: '2024-01-11', dueDate: '2024-01-20', method: 'تحويل بنكي', status: 'مدفوع', category: 'تعاقدات', type: 'other', description: 'عقد استشارات هندسية', reference: 'ENG-001' },
                { id: 6, recipient: 'مكتب التسويق الرقمي', amount: 4500, date: '2024-01-10', dueDate: '2024-01-25', method: 'بطاقة', status: 'معلق', category: 'تسويق', type: 'other', description: 'حملة إعلانية شهرية', reference: 'MKT-001' }
            ],

            // بيانات التقارير
            products: [
                { id: 'P001', name: 'شاورما لحم', sku: 'SH-BEEF-001', price: 30, cost: 18, category: 'وجبات رئيسية', supplier_id: 1, unit: 'قطعة' },
                { id: 'P002', name: 'برجر دجاج', sku: 'BG-CHKN-001', price: 25, cost: 15, category: 'وجبات رئيسية', supplier_id: 1, unit: 'قطعة' },
                { id: 'P003', name: 'بيتزا مارجريتا', sku: 'PZ-MARG-001', price: 40, cost: 22, category: 'بيتزا', supplier_id: 2, unit: 'قطعة' },
                { id: 'P004', name: 'سلطة سيزر', sku: 'SL-CZAR-001', price: 20, cost: 12, category: 'سلطات', supplier_id: 3, unit: 'قطعة' },
                { id: 'P005', name: 'عصير برتقال', sku: 'JC-ORNG-001', price: 10, cost: 5, category: 'مشروبات', supplier_id: 4, unit: 'كوب' },
                { id: 'P006', name: 'كباب مشوي', sku: 'KB-GRLD-001', price: 35, cost: 20, category: 'وجبات رئيسية', supplier_id: 1, unit: 'قطعة' },
                { id: 'P007', name: 'فلافل', sku: 'FL-TRAD-001', price: 15, cost: 8, category: 'وجبات شعبية', supplier_id: 3, unit: 'قطعة' },
                { id: 'P008', name: 'عصير مانجو', sku: 'JC-MANG-001', price: 12, cost: 6, category: 'مشروبات', supplier_id: 4, unit: 'كوب' }
            ],

            dailyReports: [
                {
                    date: '2024-01-15',
                    totalSales: 28750,
                    internalSales: { cash: 12300, card: 5900, total: 18200 },
                    externalSales: {
                        hungerstation: { amount: 4200, orders: 28, commission: 630 },
                        talabat: { amount: 3150, orders: 21, commission: 472 },
                        jahez: { amount: 2100, orders: 14, commission: 315 },
                        mrsool: { amount: 1100, orders: 7, commission: 165 }
                    },
                    itemizedSales: [
                        { product_id: 'P001', quantity: 45, total: 1350, channel: 'mixed' },
                        { product_id: 'P002', quantity: 32, total: 800, channel: 'internal' },
                        { product_id: 'P003', quantity: 18, total: 720, channel: 'external' },
                        { product_id: 'P004', quantity: 25, total: 500, channel: 'mixed' },
                        { product_id: 'P005', quantity: 40, total: 400, channel: 'internal' }
                    ],
                    purchases: 6370,
                    operatingCosts: 2832,
                    netProfit: 19548
                }
            ],
            sales: [
                { id: 1, invoice: 'INV-2024-001', customer: 'أحمد محمد', phone: '+966501234567', date: '2024-01-15', total: 402.5, method: 'نقدي', status: 'مكتمل', source: 'في المتجر', salesperson: 'فاطمة أحمد' },
                { id: 2, invoice: 'INV-2024-002', customer: 'سارة علي', date: '2024-01-14', total: 256, method: 'بطاقة', status: 'مكتمل', source: 'تطبيق توصيل', salesperson: 'محمد علي' },
                { id: 3, invoice: 'INV-2024-003', customer: 'خالد أحمد', phone: '+966509876543', date: '2024-01-13', total: 697.5, method: 'تقسيط', status: 'معلق', source: 'متجر إلكتروني', salesperson: 'أحمد محمد' }
            ]
        };

        // عرض لوحة التحكم
        function showDashboard() {
            return `
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold">مرحباً بك في نظام المحاسبة</h1>
                            <p class="text-gray-600">إليك ملخص سريع لأداء عملك اليوم</p>
                        </div>
                        <div class="text-left">
                            <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-lg shadow-lg">
                                <p class="text-sm opacity-90">الساعة الآن</p>
                                <p class="text-2xl font-bold" id="dashboard-clock">
                                    جاري التحميل...
                                </p>
                                <p class="text-xs opacity-75" id="dashboard-hijri">
                                    التاريخ الهجري
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- بطاقات المؤشرات الرئيسية -->
                    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        ${mockData.kpis.map(kpi => `
                            <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">${kpi.title}</p>
                                        <p class="text-2xl font-bold">${kpi.value}</p>
                                        <p class="text-xs text-green-600">${kpi.change} من الشهر الماضي</p>
                                    </div>
                                    <div class="text-2xl">${kpi.icon}</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>

                    <!-- الرسوم البيانية -->
                    <div class="grid gap-6 md:grid-cols-2">
                        <div class="col-span-2 rounded-lg border bg-white p-6 shadow-sm">
                            <h3 class="text-lg font-semibold mb-4">الإيرادات والمصروفات الشهرية</h3>
                            <div class="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
                                <div class="text-center">
                                    <div class="text-4xl mb-4">📊</div>
                                    <p class="text-lg font-medium">رسم بياني للإيرادات والمصروفات</p>
                                    <p class="text-sm text-gray-500 mt-2">سيتم عرض الرسوم البيانية التفاعلية هنا</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأنشطة الحديثة -->
                    <div class="grid gap-6 lg:grid-cols-3">
                        <div class="lg:col-span-2 rounded-lg border bg-white p-6 shadow-sm">
                            <h3 class="text-lg font-semibold mb-4">آخر الأنشطة</h3>
                            <div class="space-y-4">
                                ${mockData.recentActivities.map(activity => `
                                    <div class="flex items-start gap-3">
                                        <div class="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                                            📝
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-center justify-between">
                                                <p class="text-sm font-medium">${activity.title}</p>
                                                <span class="text-xs text-gray-500">${activity.time}</span>
                                            </div>
                                            <p class="text-sm text-gray-600">${activity.description}</p>
                                            ${activity.amount ? `<p class="text-sm font-medium text-blue-600">${activity.amount}</p>` : ''}
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <div class="rounded-lg border bg-white p-6 shadow-sm">
                            <h3 class="text-lg font-semibold mb-4">الإجراءات السريعة</h3>
                            <div class="grid grid-cols-2 lg:grid-cols-4 gap-3">
                                <button onclick="addNewItem('sale')" class="btn flex flex-col items-center gap-2 p-4 border rounded-lg hover:bg-gray-50 card-hover">
                                    <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-green-500 text-white">➕</div>
                                    <div class="text-center">
                                        <p class="text-sm font-medium">إضافة مبيعة</p>
                                        <p class="text-xs text-gray-500">تسجيل معاملة جديدة</p>
                                    </div>
                                </button>
                                <button onclick="addNewItem('purchase')" class="btn flex flex-col items-center gap-2 p-4 border rounded-lg hover:bg-gray-50 card-hover">
                                    <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-500 text-white">🛒</div>
                                    <div class="text-center">
                                        <p class="text-sm font-medium">طلب شراء</p>
                                        <p class="text-xs text-gray-500">إنشاء طلب جديد</p>
                                    </div>
                                </button>
                                <button onclick="testAddData()" class="btn flex flex-col items-center gap-2 p-4 border rounded-lg hover:bg-gray-50 card-hover">
                                    <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-500 text-white">🧪</div>
                                    <div class="text-center">
                                        <p class="text-sm font-medium">اختبار البيانات</p>
                                        <p class="text-xs text-gray-500">إضافة بيانات تجريبية</p>
                                    </div>
                                </button>
                                <button onclick="diagnoseSystem()" class="btn flex flex-col items-center gap-2 p-4 border rounded-lg hover:bg-gray-50 card-hover">
                                    <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-red-500 text-white">🔍</div>
                                    <div class="text-center">
                                        <p class="text-sm font-medium">تشخيص النظام</p>
                                        <p class="text-xs text-gray-500">فحص الأخطاء</p>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // عرض وحدة المشتريات
        function showPurchases() {
            const totalAmount = mockData.purchases.reduce((sum, p) => sum + p.amount, 0);
            const pendingCount = mockData.purchases.filter(p => p.status === 'في الانتظار').length;
            const receivedCount = mockData.purchases.filter(p => p.status === 'مستلم').length;

            return `
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold">إدارة المشتريات</h1>
                            <p class="text-gray-600">تتبع وإدارة جميع عمليات الشراء والموردين</p>
                        </div>
                        <button onclick="addNewItem('purchase')" class="btn bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
                            ➕ طلب شراء جديد
                        </button>
                    </div>

                    <!-- بطاقات الإحصائيات -->
                    <div class="grid gap-4 md:grid-cols-4">
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي المشتريات</p>
                                    <p class="text-2xl font-bold">${totalAmount.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">هذا الشهر</p>
                                </div>
                                <div class="text-2xl">💰</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">طلبات معلقة</p>
                                    <p class="text-2xl font-bold">${pendingCount}</p>
                                    <p class="text-xs text-gray-500">تحتاج موافقة</p>
                                </div>
                                <div class="text-2xl">⏳</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">طلبات مستلمة</p>
                                    <p class="text-2xl font-bold">${receivedCount}</p>
                                    <p class="text-xs text-gray-500">تم الاستلام</p>
                                </div>
                                <div class="text-2xl">📦</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">عدد الموردين</p>
                                    <p class="text-2xl font-bold">${mockData.suppliers.length}</p>
                                    <p class="text-xs text-gray-500">مورد نشط</p>
                                </div>
                                <div class="text-2xl">🏢</div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول المشتريات -->
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold">قائمة المشتريات</h3>
                                <div class="flex items-center gap-2">
                                    <input type="text" placeholder="البحث في المشتريات..." class="px-3 py-2 border rounded-lg text-sm">
                                    <button class="px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">🔍</button>
                                    <button onclick="forceReloadSection('purchases')" class="px-3 py-2 border rounded-lg text-sm hover:bg-gray-50" title="إعادة تحميل">🔄</button>
                                </div>
                            </div>
                        </div>
                        <div class="table-container">
                            <table class="table-fixed">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="text-center p-4 font-medium w-1/5">رقم الفاتورة</th>
                                        <th class="text-center p-4 font-medium w-1/4">التاريخ</th>
                                        <th class="text-center p-4 font-medium w-1/4">المبلغ</th>
                                        <th class="text-center p-4 font-medium w-1/5">الحالة</th>
                                        <th class="text-center p-4 font-medium w-1/5">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${mockData.purchases.map(purchase => `
                                        <tr class="table-row border-b hover:bg-gray-50">
                                            <td class="p-4 font-medium text-center" title="${purchase.invoice}">${purchase.invoice}</td>
                                            <td class="p-4 text-center" title="${purchase.date}">${purchase.date}</td>
                                            <td class="p-4 font-medium text-center" title="${purchase.amount.toLocaleString()} ر.س">${purchase.amount.toLocaleString()} ر.س</td>
                                            <td class="p-4 text-center">
                                                <span class="status-badge inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                                    purchase.status === 'مستلم' ? 'bg-green-100 text-green-800' :
                                                    purchase.status === 'معتمد' ? 'bg-blue-100 text-blue-800' :
                                                    'bg-yellow-100 text-yellow-800'
                                                }">
                                                    ${purchase.status}
                                                </span>
                                            </td>
                                            <td class="p-4 text-center">
                                                <div class="flex items-center justify-center gap-1">
                                                    <button type="button" onclick="viewItem('purchase', ${purchase.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="عرض التفاصيل">👁️</button>
                                                    <button type="button" onclick="editItem('purchase', ${purchase.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="تعديل">✏️</button>
                                                    <button type="button" onclick="deleteItem('purchase', ${purchase.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="حذف">🗑️</button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        }

        // دالة لعرض محتوى تبويبات المدفوعات
        function getPaymentTabContent(tab) {
            if (tab === 'suppliers') {
                return `
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold">🏢 مدفوعات الموردين</h3>
                                <div class="flex items-center gap-2">
                                    <input type="text" placeholder="البحث في مدفوعات الموردين..." class="px-3 py-2 border rounded-lg text-sm">
                                    <button class="px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">🔍</button>
                                    <button onclick="forceReloadSection('payments')" class="px-3 py-2 border rounded-lg text-sm hover:bg-gray-50" title="إعادة تحميل">🔄</button>
                                </div>
                            </div>
                        </div>
                        <div class="table-container">
                            <table class="table-fixed">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="text-center p-4 font-medium w-1/6">المورد</th>
                                        <th class="text-center p-4 font-medium w-1/6">المبلغ</th>
                                        <th class="text-center p-4 font-medium w-1/6">تاريخ الدفع</th>
                                        <th class="text-center p-4 font-medium w-1/6">طريقة الدفع</th>
                                        <th class="text-center p-4 font-medium w-1/6">الحالة</th>
                                        <th class="text-center p-4 font-medium w-1/6">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${mockData.payments.map(payment => `
                                        <tr class="table-row border-b">
                                            <td class="p-4 font-medium text-center" title="${payment.recipient}">${payment.recipient}</td>
                                            <td class="p-4 font-medium text-center" title="${payment.amount.toLocaleString()} ر.س">${payment.amount.toLocaleString()} ر.س</td>
                                            <td class="p-4 text-center" title="${payment.date}">${payment.date}</td>
                                            <td class="p-4 text-center" title="${payment.method}">${payment.method}</td>
                                            <td class="p-4 text-center">
                                                <span class="status-badge inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                                    payment.status === 'مدفوع' ? 'bg-green-100 text-green-800' :
                                                    payment.status === 'معلق' ? 'bg-yellow-100 text-yellow-800' :
                                                    'bg-red-100 text-red-800'
                                                }">
                                                    ${payment.status}
                                                </span>
                                            </td>
                                            <td class="p-4 text-center">
                                                <div class="flex items-center justify-center gap-1">
                                                    <button type="button" onclick="viewItem('payment', ${payment.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="عرض التفاصيل">👁️</button>
                                                    <button type="button" onclick="editItem('payment', ${payment.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="تعديل">✏️</button>
                                                    <button type="button" onclick="deleteItem('payment', ${payment.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="حذف">🗑️</button>
                                                    ${payment.status === 'معلق' ? '<button type="button" onclick="confirmPayment(' + payment.id + ')" class="icon-btn p-1 hover:bg-gray-100 rounded text-green-600" title="تأكيد الدفع">✅</button>' : ''}
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;
            } else {
                return `
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold">💼 مدفوعات أخرى</h3>
                                <div class="flex items-center gap-2">
                                    <input type="text" placeholder="البحث في المدفوعات الأخرى..." class="px-3 py-2 border rounded-lg text-sm">
                                    <button class="px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">🔍</button>
                                    <button onclick="forceReloadSection('payments')" class="px-3 py-2 border rounded-lg text-sm hover:bg-gray-50" title="إعادة تحميل">🔄</button>
                                </div>
                            </div>
                        </div>
                        <div class="table-container">
                            <table class="table-fixed">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="text-center p-4 font-medium w-1/7">المستفيد</th>
                                        <th class="text-center p-4 font-medium w-1/7">المبلغ</th>
                                        <th class="text-center p-4 font-medium w-1/7">تاريخ الدفع</th>
                                        <th class="text-center p-4 font-medium w-1/7">طريقة الدفع</th>
                                        <th class="text-center p-4 font-medium w-1/7">الفئة</th>
                                        <th class="text-center p-4 font-medium w-1/7">الحالة</th>
                                        <th class="text-center p-4 font-medium w-1/7">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${mockData.otherPayments.map(payment => `
                                        <tr class="table-row border-b">
                                            <td class="p-4 font-medium text-center" title="${payment.recipient}">${payment.recipient}</td>
                                            <td class="p-4 font-medium text-center" title="${payment.amount.toLocaleString()} ر.س">${payment.amount.toLocaleString()} ر.س</td>
                                            <td class="p-4 text-center" title="${payment.date}">${payment.date}</td>
                                            <td class="p-4 text-center" title="${payment.method}">${payment.method}</td>
                                            <td class="p-4 text-center" title="${payment.category}">${payment.category}</td>
                                            <td class="p-4 text-center">
                                                <span class="status-badge inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                                    payment.status === 'مدفوع' ? 'bg-green-100 text-green-800' :
                                                    payment.status === 'معلق' ? 'bg-yellow-100 text-yellow-800' :
                                                    'bg-red-100 text-red-800'
                                                }">
                                                    ${payment.status}
                                                </span>
                                            </td>
                                            <td class="p-4 text-center">
                                                <div class="flex items-center justify-center gap-1">
                                                    <button type="button" onclick="viewItem('otherPayment', ${payment.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="عرض التفاصيل">👁️</button>
                                                    <button type="button" onclick="editItem('otherPayment', ${payment.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="تعديل">✏️</button>
                                                    <button type="button" onclick="deleteItem('otherPayment', ${payment.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="حذف">🗑️</button>
                                                    ${payment.status === 'معلق' ? '<button type="button" onclick="confirmOtherPayment(' + payment.id + ')" class="icon-btn p-1 hover:bg-gray-100 rounded text-green-600" title="تأكيد الدفع">✅</button>' : ''}
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;
            }
        }

        // دالة لتبديل التبويبات
        function showPaymentTab(tab) {
            activePaymentTab = tab;

            // تحديث أزرار التبويبات
            document.querySelectorAll('.payment-tab').forEach(btn => {
                btn.classList.remove('active', 'border-blue-500', 'text-blue-600');
                btn.classList.add('border-transparent', 'text-gray-500');
            });

            const activeBtn = document.getElementById(tab + '-tab');
            if (activeBtn) {
                activeBtn.classList.add('active', 'border-blue-500', 'text-blue-600');
                activeBtn.classList.remove('border-transparent', 'text-gray-500');
            }

            // تحديث المحتوى
            const contentDiv = document.getElementById('payment-tabs-content');
            if (contentDiv) {
                contentDiv.innerHTML = getPaymentTabContent(tab);
            }
        }

        // دالة لتحديث بيانات الموردين بناءً على المشتريات (محسنة لمنع المضاعفة)
        function updateSuppliersData() {
            console.log('🔄 تحديث بيانات الموردين...');

            mockData.suppliers.forEach(supplier => {
                // حساب إجمالي المشتريات لكل مورد مع معالجة صحيحة للأرقام
                const supplierPurchases = mockData.purchases.filter(p => p.supplier_id === supplier.id);
                const totalPurchases = supplierPurchases.reduce((sum, p) => sum + parseNumber(p.amount), 0);
                supplier.totalPurchases = totalPurchases;

                // حساب عدد المشتريات
                supplier.purchaseCount = supplierPurchases.length;

                // حساب آخر تاريخ شراء
                if (supplierPurchases.length > 0) {
                    const latestPurchase = supplierPurchases.sort((a, b) => new Date(b.date) - new Date(a.date))[0];
                    supplier.lastPurchaseDate = latestPurchase.date;
                }

                // حساب إجمالي المدفوعات لهذا المورد مع معالجة صحيحة للأرقام
                const supplierPayments = mockData.payments.filter(p => p.supplier_id === supplier.id);
                const totalPayments = supplierPayments.reduce((sum, p) => sum + parseNumber(p.amount), 0);
                supplier.totalPayments = totalPayments;
                supplier.paymentsCount = supplierPayments.length;

                // حساب آخر تاريخ دفعة
                if (supplierPayments.length > 0) {
                    const latestPayment = supplierPayments.sort((a, b) => new Date(b.date) - new Date(a.date))[0];
                    supplier.lastPaymentDate = latestPayment.date;
                }

                // حساب الرصيد الحالي مباشرة (بدون استدعاء دالة أخرى لمنع المضاعفة)
                const previousBalance = parseNumber(supplier.previousBalance) || 0;
                const currentBalance = previousBalance + totalPurchases - totalPayments;
                supplier.balance = currentBalance;

                // حساب إجمالي المستحقات (فقط الأرصدة الموجبة)
                supplier.totalOutstanding = Math.max(0, currentBalance);

                console.log(`📊 المورد ${supplier.name}:`);
                console.log(`   💰 الرصيد السابق: ${formatNumber(previousBalance)} ر.س`);
                console.log(`   📦 إجمالي المشتريات: ${formatNumber(totalPurchases)} ر.س`);
                console.log(`   💳 إجمالي المدفوعات: ${formatNumber(totalPayments)} ر.س`);
                console.log(`   💰 الرصيد الحالي: ${formatNumber(currentBalance)} ر.س`);
                console.log(`   📊 إجمالي المستحقات: ${formatNumber(supplier.totalOutstanding)} ر.س`);
            });

            console.log('✅ تم تحديث بيانات جميع الموردين بدون مضاعفة');
        }

        // عرض وحدة الموردين
        function showSuppliers() {
            // تحديث بيانات الموردين أولاً
            updateSuppliersData();

            const totalSuppliers = mockData.suppliers.length;
            const totalPurchases = mockData.suppliers.reduce((sum, s) => sum + s.totalPurchases, 0);
            const totalOutstanding = mockData.suppliers.reduce((sum, s) => sum + s.totalOutstanding, 0);

            // حساب عدد الموردين النشطين (الذين لديهم مشتريات)
            const activeSuppliers = mockData.suppliers.filter(s => s.totalPurchases > 0).length;

            // حساب متوسط قيمة المشتريات
            const avgPurchaseValue = activeSuppliers > 0 ? Math.round(totalPurchases / activeSuppliers) : 0;

            return `
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold">إدارة الموردين</h1>
                            <p class="text-gray-600">إدارة معلومات الموردين وتتبع المعاملات التجارية</p>
                        </div>
                        <button onclick="addNewItem('supplier')" class="btn bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
                            ➕ مورد جديد
                        </button>
                    </div>

                    <!-- بطاقات الإحصائيات -->
                    <div class="grid gap-4 md:grid-cols-4">
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي الموردين</p>
                                    <p class="text-2xl font-bold">${totalSuppliers}</p>
                                    <p class="text-xs text-gray-500">نشط: ${activeSuppliers}</p>
                                </div>
                                <div class="text-2xl">🏢</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي المشتريات</p>
                                    <p class="text-2xl font-bold">${totalPurchases.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">من البيانات الفعلية</p>
                                </div>
                                <div class="text-2xl">💰</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي المستحقات</p>
                                    <p class="text-2xl font-bold">${totalOutstanding.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">حالي + سابق</p>
                                </div>
                                <div class="text-2xl">📊</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">متوسط قيمة المشتريات</p>
                                    <p class="text-2xl font-bold">${avgPurchaseValue.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">لكل مورد نشط</p>
                                </div>
                                <div class="text-2xl">📊</div>
                            </div>
                        </div>
                    </div>

                    <!-- بطاقات الموردين -->
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold">قائمة الموردين</h3>
                                <div class="flex items-center gap-2">
                                    <input type="text" placeholder="البحث في الموردين..." class="px-3 py-2 border rounded-lg text-sm">
                                    <button class="px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">🔍</button>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                ${mockData.suppliers.map(supplier => `
                                    <div class="supplier-card border rounded-lg p-4 bg-white shadow-sm">
                                        <div class="flex items-start justify-between mb-3">
                                            <div>
                                                <h4 class="text-lg font-semibold">${supplier.name}</h4>
                                                <p class="text-sm text-gray-600">${supplier.contact}</p>
                                            </div>
                                            <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-green-100 text-green-800">
                                                نشط
                                            </span>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="flex items-center gap-2 text-sm">
                                                <span>📞</span>
                                                <span>${supplier.phone}</span>
                                            </div>
                                            <div class="flex items-center gap-2 text-sm">
                                                <span>📧</span>
                                                <span class="truncate">${supplier.email}</span>
                                            </div>
                                            <div class="flex items-center gap-2 text-sm">
                                                <span>📍</span>
                                                <span>${supplier.city}</span>
                                            </div>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="flex justify-between text-sm">
                                                <span class="text-gray-600">إجمالي المشتريات:</span>
                                                <span class="font-medium">${supplier.totalPurchases.toLocaleString()} ر.س</span>
                                            </div>
                                            <div class="flex justify-between text-sm">
                                                <span class="text-gray-600">إجمالي المدفوعات:</span>
                                                <span class="font-medium text-green-600">${(supplier.totalPayments || 0).toLocaleString()} ر.س</span>
                                            </div>
                                            <div class="flex justify-between text-sm border-t pt-2 mt-2">
                                                <span class="text-gray-600 font-medium">إجمالي المستحقات:</span>
                                                <span class="font-bold text-orange-600">${supplier.totalOutstanding.toLocaleString()} ر.س</span>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <button onclick="viewItem('supplier', ${supplier.id})" class="btn flex-1 px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">👁️ عرض</button>
                                            <button onclick="editItem('supplier', ${supplier.id})" class="btn flex-1 px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">✏️ تعديل</button>
                                            <button onclick="deleteItem('supplier', ${supplier.id})" class="btn px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">🗑️</button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // عرض وحدة الموظفين
        function showEmployees() {
            const totalEmployees = mockData.employees.length;
            const activeEmployees = mockData.employees.filter(e => e.status === 'نشط').length;
            const totalSalaries = mockData.employees.reduce((sum, e) => sum + e.salary, 0);
            const onLeaveCount = mockData.employees.filter(e => e.status === 'في إجازة').length;

            return `
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold">إدارة الموظفين</h1>
                            <p class="text-gray-600">إدارة معلومات الموظفين والرواتب والحضور</p>
                        </div>
                        <button onclick="addNewItem('employee')" class="btn bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
                            ➕ موظف جديد
                        </button>
                    </div>

                    <!-- بطاقات الإحصائيات -->
                    <div class="grid gap-4 md:grid-cols-4">
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي الموظفين</p>
                                    <p class="text-2xl font-bold">${totalEmployees}</p>
                                    <p class="text-xs text-gray-500">${activeEmployees} نشط</p>
                                </div>
                                <div class="text-2xl">👥</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي الرواتب</p>
                                    <p class="text-2xl font-bold">${totalSalaries.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">شهرياً</p>
                                </div>
                                <div class="text-2xl">💰</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">موظفين في إجازة</p>
                                    <p class="text-2xl font-bold">${onLeaveCount}</p>
                                    <p class="text-xs text-gray-500">حالياً</p>
                                </div>
                                <div class="text-2xl">🏖️</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">متوسط الراتب</p>
                                    <p class="text-2xl font-bold">${Math.round(totalSalaries / totalEmployees).toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">للموظف الواحد</p>
                                </div>
                                <div class="text-2xl">📊</div>
                            </div>
                        </div>
                    </div>

                    <!-- بطاقات الموظفين -->
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold">قائمة الموظفين</h3>
                                <div class="flex items-center gap-2">
                                    <input type="text" placeholder="البحث في الموظفين..." class="px-3 py-2 border rounded-lg text-sm">
                                    <button class="px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">🔍</button>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                ${mockData.employees.map(employee => `
                                    <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                                        <div class="flex items-start justify-between mb-3">
                                            <div>
                                                <h4 class="text-lg font-semibold">${employee.name}</h4>
                                                <p class="text-sm text-gray-600">${employee.position}</p>
                                                <p class="text-xs text-gray-500">${employee.department}</p>
                                            </div>
                                            <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                                employee.status === 'نشط' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                                            }">
                                                ${employee.status}
                                            </span>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="flex items-center gap-2 text-sm">
                                                <span>📞</span>
                                                <span>${employee.phone}</span>
                                            </div>
                                            <div class="flex items-center gap-2 text-sm">
                                                <span>📧</span>
                                                <span class="truncate">${employee.email}</span>
                                            </div>
                                            <div class="flex items-center gap-2 text-sm">
                                                <span>🆔</span>
                                                <span>${employee.employeeId}</span>
                                            </div>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="flex justify-between text-sm">
                                                <span class="text-gray-600">الراتب:</span>
                                                <span class="font-medium">${employee.salary.toLocaleString()} ر.س</span>
                                            </div>
                                            <div class="flex justify-between text-sm">
                                                <span class="text-gray-600">القسم:</span>
                                                <span class="font-medium">${employee.department}</span>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <button onclick="viewItem('employee', ${employee.id})" class="btn flex-1 px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">👁️ عرض</button>
                                            <button onclick="editItem('employee', ${employee.id})" class="btn flex-1 px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">✏️ تعديل</button>
                                            <button onclick="deleteItem('employee', ${employee.id})" class="btn px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">🗑️</button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // عرض وحدة المصروفات اليومية
        function showExpenses() {
            const totalExpenses = mockData.expenses.reduce((sum, e) => sum + e.amount, 0);
            const approvedExpenses = mockData.expenses.filter(e => e.status === 'معتمد').reduce((sum, e) => sum + e.amount, 0);
            const pendingCount = mockData.expenses.filter(e => e.status === 'في الانتظار').length;

            // تجميع المصروفات حسب الفئة
            const categories = ['مرافق', 'مواصلات', 'مكتب', 'تقنية'];
            const expensesByCategory = categories.map(category => ({
                category,
                amount: mockData.expenses.filter(e => e.category === category).reduce((sum, e) => sum + e.amount, 0),
                count: mockData.expenses.filter(e => e.category === category).length
            })).filter(item => item.count > 0);

            return `
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold">المصروفات اليومية</h1>
                            <p class="text-gray-600">تتبع وإدارة جميع المصروفات اليومية والتشغيلية</p>
                        </div>
                        <button onclick="addNewItem('expense')" class="btn bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
                            ➕ مصروف جديد
                        </button>
                    </div>

                    <!-- بطاقات الإحصائيات -->
                    <div class="grid gap-4 md:grid-cols-4">
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي المصروفات</p>
                                    <p class="text-2xl font-bold">${totalExpenses.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">هذا الشهر</p>
                                </div>
                                <div class="text-2xl">💰</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">مصروفات معتمدة</p>
                                    <p class="text-2xl font-bold">${approvedExpenses.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">تم اعتمادها</p>
                                </div>
                                <div class="text-2xl">✅</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">مصروفات معلقة</p>
                                    <p class="text-2xl font-bold">${pendingCount}</p>
                                    <p class="text-xs text-gray-500">تحتاج موافقة</p>
                                </div>
                                <div class="text-2xl">⏳</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">متوسط المصروف</p>
                                    <p class="text-2xl font-bold">${Math.round(totalExpenses / mockData.expenses.length).toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">للمصروف الواحد</p>
                                </div>
                                <div class="text-2xl">📊</div>
                            </div>
                        </div>
                    </div>

                    <!-- المصروفات حسب الفئة -->
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <h3 class="text-lg font-semibold">المصروفات حسب الفئة</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                                ${expensesByCategory.map(item => `
                                    <div class="flex items-center justify-between p-3 border rounded-lg">
                                        <div>
                                            <p class="font-medium">${item.category}</p>
                                            <p class="text-sm text-gray-500">${item.count} مصروف</p>
                                        </div>
                                        <div class="text-left">
                                            <p class="font-bold">${item.amount.toLocaleString()} ر.س</p>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>

                    <!-- جدول المصروفات -->
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold">قائمة المصروفات</h3>
                                <div class="flex items-center gap-2">
                                    <select class="px-3 py-2 border rounded-lg text-sm">
                                        <option>جميع الفئات</option>
                                        <option>مرافق</option>
                                        <option>مواصلات</option>
                                        <option>مكتب</option>
                                        <option>تقنية</option>
                                    </select>
                                    <input type="text" placeholder="البحث في المصروفات..." class="px-3 py-2 border rounded-lg text-sm">
                                    <button class="px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">🔍</button>
                                </div>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="text-right p-4 font-medium">الوصف</th>
                                        <th class="text-right p-4 font-medium">المبلغ</th>
                                        <th class="text-right p-4 font-medium">التاريخ</th>
                                        <th class="text-right p-4 font-medium">الفئة</th>
                                        <th class="text-right p-4 font-medium">طريقة الدفع</th>
                                        <th class="text-right p-4 font-medium">الحالة</th>
                                        <th class="text-right p-4 font-medium">مضاف بواسطة</th>
                                        <th class="text-right p-4 font-medium">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${mockData.expenses.map(expense => `
                                        <tr class="border-b hover:bg-gray-50">
                                            <td class="p-4 font-medium">${expense.description}</td>
                                            <td class="p-4 font-medium">${expense.amount.toLocaleString()} ر.س</td>
                                            <td class="p-4">${expense.date}</td>
                                            <td class="p-4">${expense.category}</td>
                                            <td class="p-4">${expense.method}</td>
                                            <td class="p-4">
                                                <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                                    expense.status === 'معتمد' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                                                }">
                                                    ${expense.status}
                                                </span>
                                            </td>
                                            <td class="p-4">${expense.addedBy}</td>
                                            <td class="p-4">
                                                <div class="flex items-center gap-2">
                                                    <button onclick="viewItem('expense', ${expense.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="عرض التفاصيل">👁️</button>
                                                    <button onclick="editItem('expense', ${expense.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="تعديل">✏️</button>
                                                    <button onclick="deleteItem('expense', ${expense.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="حذف">🗑️</button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        }

        // عرض وحدة إدخال المبيعات
        function showSales() {
            const totalSales = mockData.sales.reduce((sum, s) => sum + s.total, 0);
            const completedSales = mockData.sales.filter(s => s.status === 'مكتمل');
            const totalCompleted = completedSales.reduce((sum, s) => sum + s.total, 0);
            const pendingCount = mockData.sales.filter(s => s.status === 'معلق').length;
            const averageSale = completedSales.length > 0 ? totalCompleted / completedSales.length : 0;

            // تجميع المبيعات حسب المصدر
            const sources = ['في المتجر', 'تطبيق توصيل', 'متجر إلكتروني'];
            const salesBySource = sources.map(source => ({
                source,
                count: mockData.sales.filter(s => s.source === source).length,
                total: mockData.sales.filter(s => s.source === source).reduce((sum, s) => sum + s.total, 0)
            })).filter(item => item.count > 0);

            return `
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold">إدخال المبيعات</h1>
                            <p class="text-gray-600">تسجيل وإدارة جميع المبيعات من مختلف المصادر</p>
                        </div>
                        <div class="flex gap-2">
                            <button onclick="addNewItem('sale')" class="btn bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
                                ➕ مبيعة جديدة
                            </button>
                            <button onclick="addDailyReport()" class="btn bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2">
                                📊 إدخال مبيعات يومية
                            </button>
                        </div>
                    </div>

                    <!-- بطاقات الإحصائيات -->
                    <div class="grid gap-4 md:grid-cols-4">
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي المبيعات</p>
                                    <p class="text-2xl font-bold">${totalSales.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">هذا الشهر</p>
                                </div>
                                <div class="text-2xl">📈</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">مبيعات مكتملة</p>
                                    <p class="text-2xl font-bold">${totalCompleted.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">${completedSales.length} معاملة</p>
                                </div>
                                <div class="text-2xl">✅</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">مبيعات معلقة</p>
                                    <p class="text-2xl font-bold">${pendingCount}</p>
                                    <p class="text-xs text-gray-500">تحتاج متابعة</p>
                                </div>
                                <div class="text-2xl">⏳</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">متوسط المبيعة</p>
                                    <p class="text-2xl font-bold">${Math.round(averageSale).toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">للمعاملة الواحدة</p>
                                </div>
                                <div class="text-2xl">📊</div>
                            </div>
                        </div>
                    </div>

                    <!-- المبيعات حسب المصدر -->
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <h3 class="text-lg font-semibold">المبيعات حسب المصدر</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                ${salesBySource.map(item => `
                                    <div class="flex items-center justify-between p-3 border rounded-lg">
                                        <div class="flex items-center gap-2">
                                            <span>${item.source === 'في المتجر' ? '🏪' : item.source === 'تطبيق توصيل' ? '🚚' : '🌐'}</span>
                                            <div>
                                                <p class="font-medium">${item.source}</p>
                                                <p class="text-sm text-gray-500">${item.count} معاملة</p>
                                            </div>
                                        </div>
                                        <div class="text-left">
                                            <p class="font-bold">${item.total.toLocaleString()} ر.س</p>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>

                    <!-- جدول المبيعات -->
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold">قائمة المبيعات</h3>
                                <div class="flex items-center gap-2">
                                    <input type="text" placeholder="البحث في المبيعات..." class="px-3 py-2 border rounded-lg text-sm">
                                    <button class="px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">🔍</button>
                                </div>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="text-right p-4 font-medium">رقم الفاتورة</th>
                                        <th class="text-right p-4 font-medium">العميل</th>
                                        <th class="text-right p-4 font-medium">التاريخ</th>
                                        <th class="text-right p-4 font-medium">المبلغ الإجمالي</th>
                                        <th class="text-right p-4 font-medium">طريقة الدفع</th>
                                        <th class="text-right p-4 font-medium">المصدر</th>
                                        <th class="text-right p-4 font-medium">الحالة</th>
                                        <th class="text-right p-4 font-medium">موظف المبيعات</th>
                                        <th class="text-right p-4 font-medium">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${mockData.sales.map(sale => `
                                        <tr class="border-b hover:bg-gray-50">
                                            <td class="p-4 font-medium">${sale.invoice}</td>
                                            <td class="p-4">
                                                <div>
                                                    <p class="font-medium">${sale.customer}</p>
                                                    ${sale.phone ? `<p class="text-sm text-gray-500">${sale.phone}</p>` : ''}
                                                </div>
                                            </td>
                                            <td class="p-4">${sale.date}</td>
                                            <td class="p-4 font-medium">${sale.total.toLocaleString()} ر.س</td>
                                            <td class="p-4">${sale.method}</td>
                                            <td class="p-4">
                                                <div class="flex items-center gap-2">
                                                    <span>${sale.source === 'في المتجر' ? '🏪' : sale.source === 'تطبيق توصيل' ? '🚚' : '🌐'}</span>
                                                    <span>${sale.source}</span>
                                                </div>
                                            </td>
                                            <td class="p-4">
                                                <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                                    sale.status === 'مكتمل' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                                                }">
                                                    ${sale.status}
                                                </span>
                                            </td>
                                            <td class="p-4">${sale.salesperson}</td>
                                            <td class="p-4">
                                                <div class="flex items-center gap-2">
                                                    <button onclick="viewItem('sale', ${sale.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="عرض التفاصيل">👁️</button>
                                                    <button onclick="editItem('sale', ${sale.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="تعديل">✏️</button>
                                                    <button onclick="deleteItem('sale', ${sale.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="حذف">🗑️</button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        }

        // عرض وحدة التقارير
        function showReports() {
            return `
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold">📊 نظام التقارير المتكامل</h1>
                            <p class="text-gray-600">تقارير شاملة للمبيعات والأرباح والأداء المالي</p>
                        </div>
                        <div class="flex gap-2">
                            <button onclick="exportReport()" class="btn bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2">
                                📥 تصدير التقرير
                            </button>
                        </div>
                    </div>

                    <!-- تبويبات التقارير -->
                    <div class="border-b border-gray-200">
                        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                            <button onclick="showReportTab('daily')" id="daily-report-tab" class="report-tab active border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-600">
                                📅 التقارير اليومية
                            </button>
                            <button onclick="showReportTab('weekly')" id="weekly-report-tab" class="report-tab border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                📈 التقارير الأسبوعية
                            </button>
                            <button onclick="showReportTab('monthly')" id="monthly-report-tab" class="report-tab border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                📊 التقارير الشهرية
                            </button>
                            <button onclick="showReportTab('products')" id="products-report-tab" class="report-tab border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                📦 قاعدة المنتجات
                            </button>
                        </nav>
                    </div>

                    <!-- محتوى التبويبات -->
                    <div id="report-tabs-content">
                        ${getReportTabContent('daily')}
                    </div>
                </div>
            `;
        }

        // متغير لتتبع التبويب النشط في التقارير
        let activeReportTab = 'daily';

        // دالة لعرض محتوى تبويبات التقارير
        function getReportTabContent(tab) {
            switch(tab) {
                case 'daily':
                    return getDailyReportContent();
                case 'weekly':
                    return getWeeklyReportContent();
                case 'monthly':
                    return getMonthlyReportContent();
                case 'products':
                    return getProductsReportContent();
                default:
                    return getDailyReportContent();
            }
        }

        // محتوى التقرير اليومي
        function getDailyReportContent() {
            const today = new Date().toISOString().split('T')[0];
            const todayReport = mockData.dailyReports.find(r => r.date === today) || mockData.dailyReports[0];

            return `
                <div class="space-y-6">
                    <!-- إحصائيات سريعة -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-blue-100">إجمالي المبيعات</p>
                                    <p class="text-2xl font-bold">${todayReport.totalSales.toLocaleString()} ر.س</p>
                                </div>
                                <div class="text-3xl">💰</div>
                            </div>
                        </div>

                        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-green-100">صافي الربح</p>
                                    <p class="text-2xl font-bold">${todayReport.netProfit.toLocaleString()} ر.س</p>
                                </div>
                                <div class="text-3xl">📈</div>
                            </div>
                        </div>

                        <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-purple-100">المبيعات الداخلية</p>
                                    <p class="text-2xl font-bold">${todayReport.internalSales.total.toLocaleString()} ر.س</p>
                                </div>
                                <div class="text-3xl">🏪</div>
                            </div>
                        </div>

                        <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-orange-100">المبيعات الخارجية</p>
                                    <p class="text-2xl font-bold">${(todayReport.totalSales - todayReport.internalSales.total).toLocaleString()} ر.س</p>
                                </div>
                                <div class="text-3xl">🚚</div>
                            </div>
                        </div>
                    </div>

                    <!-- تفصيل المبيعات الخارجية -->
                    <div class="bg-white rounded-lg border shadow-sm">
                        <div class="p-6 border-b">
                            <h3 class="text-lg font-semibold">🚚 تفصيل المبيعات الخارجية</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                ${Object.entries(todayReport.externalSales).map(([platform, data]) => `
                                    <div class="border rounded-lg p-4">
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 class="font-medium">${getPlatformName(platform)}</h4>
                                            <span class="text-sm text-gray-500">${data.orders} طلب</span>
                                        </div>
                                        <p class="text-xl font-bold text-green-600">${data.amount.toLocaleString()} ر.س</p>
                                        <p class="text-sm text-red-500">عمولة: ${data.commission.toLocaleString()} ر.س</p>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>

                    <!-- أفضل المنتجات مبيعاً -->
                    <div class="bg-white rounded-lg border shadow-sm">
                        <div class="p-6 border-b">
                            <h3 class="text-lg font-semibold">📦 أفضل المنتجات مبيعاً</h3>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="text-right p-4 font-medium">المنتج</th>
                                        <th class="text-right p-4 font-medium">الكمية</th>
                                        <th class="text-right p-4 font-medium">الإجمالي</th>
                                        <th class="text-right p-4 font-medium">القناة</th>
                                        <th class="text-right p-4 font-medium">الربح</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${todayReport.itemizedSales.map(item => {
                                        const product = mockData.products.find(p => p.id === item.product_id);
                                        const profit = item.total - (product.cost * item.quantity);
                                        return `
                                            <tr class="border-b hover:bg-gray-50">
                                                <td class="p-4 font-medium">${product.name}</td>
                                                <td class="p-4">${item.quantity} ${product.unit}</td>
                                                <td class="p-4 font-medium">${item.total.toLocaleString()} ر.س</td>
                                                <td class="p-4">${getChannelName(item.channel)}</td>
                                                <td class="p-4 font-medium text-green-600">${profit.toLocaleString()} ر.س</td>
                                            </tr>
                                        `;
                                    }).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        }

        // دوال مساعدة للتقارير
        function getPlatformName(platform) {
            const names = {
                hungerstation: 'هنقرستيشن',
                talabat: 'طلبات',
                jahez: 'جاهز',
                mrsool: 'مرسول'
            };
            return names[platform] || platform;
        }

        function getChannelName(channel) {
            const names = {
                internal: 'داخلي',
                external: 'خارجي',
                mixed: 'مختلط'
            };
            return names[channel] || channel;
        }

        // محتوى التقرير الأسبوعي
        function getWeeklyReportContent() {
            return `
                <div class="space-y-6">
                    <div class="bg-white rounded-lg border shadow-sm">
                        <div class="p-6 border-b">
                            <h3 class="text-lg font-semibold">📈 التقرير الأسبوعي</h3>
                        </div>
                        <div class="p-6">
                            <div class="text-center text-gray-500">
                                <div class="text-6xl mb-4">📊</div>
                                <p class="text-lg">التقارير الأسبوعية قيد التطوير</p>
                                <p class="text-sm">سيتم إضافة تحليل شامل للأداء الأسبوعي قريباً</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // محتوى التقرير الشهري
        function getMonthlyReportContent() {
            return `
                <div class="space-y-6">
                    <div class="bg-white rounded-lg border shadow-sm">
                        <div class="p-6 border-b">
                            <h3 class="text-lg font-semibold">📊 التقرير الشهري</h3>
                        </div>
                        <div class="p-6">
                            <div class="text-center text-gray-500">
                                <div class="text-6xl mb-4">📈</div>
                                <p class="text-lg">التقارير الشهرية قيد التطوير</p>
                                <p class="text-sm">سيتم إضافة تحليل مالي شامل وحساب الأرباح الشهرية قريباً</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // محتوى قاعدة المنتجات
        function getProductsReportContent() {
            return `
                <div class="space-y-6">
                    <div class="bg-white rounded-lg border shadow-sm">
                        <div class="p-6 border-b">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold">📦 قاعدة بيانات المنتجات</h3>
                                <button onclick="addNewProduct()" class="btn bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
                                    ➕ منتج جديد
                                </button>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="text-right p-4 font-medium">الرمز</th>
                                        <th class="text-right p-4 font-medium">اسم المنتج</th>
                                        <th class="text-right p-4 font-medium">السعر</th>
                                        <th class="text-right p-4 font-medium">التكلفة</th>
                                        <th class="text-right p-4 font-medium">الربح</th>
                                        <th class="text-right p-4 font-medium">الفئة</th>
                                        <th class="text-right p-4 font-medium">الوحدة</th>
                                        <th class="text-right p-4 font-medium">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${mockData.products.map(product => `
                                        <tr class="border-b hover:bg-gray-50">
                                            <td class="p-4 font-mono text-sm">${product.sku}</td>
                                            <td class="p-4 font-medium">${product.name}</td>
                                            <td class="p-4">${product.price} ر.س</td>
                                            <td class="p-4">${product.cost} ر.س</td>
                                            <td class="p-4 font-medium text-green-600">${(product.price - product.cost)} ر.س</td>
                                            <td class="p-4">${product.category}</td>
                                            <td class="p-4">${product.unit}</td>
                                            <td class="p-4">
                                                <div class="flex items-center gap-2">
                                                    <button onclick="editProduct('${product.id}')" class="icon-btn p-1 hover:bg-gray-100 rounded" title="تعديل">✏️</button>
                                                    <button onclick="deleteProduct('${product.id}')" class="icon-btn p-1 hover:bg-gray-100 rounded" title="حذف">🗑️</button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        }

        // دالة لتبديل تبويبات التقارير
        function showReportTab(tab) {
            activeReportTab = tab;

            // تحديث أزرار التبويبات
            document.querySelectorAll('.report-tab').forEach(btn => {
                btn.classList.remove('active', 'border-blue-500', 'text-blue-600');
                btn.classList.add('border-transparent', 'text-gray-500');
            });

            const activeBtn = document.getElementById(tab + '-report-tab');
            if (activeBtn) {
                activeBtn.classList.add('active', 'border-blue-500', 'text-blue-600');
                activeBtn.classList.remove('border-transparent', 'text-gray-500');
            }

            // تحديث المحتوى
            const contentDiv = document.getElementById('report-tabs-content');
            if (contentDiv) {
                contentDiv.innerHTML = getReportTabContent(tab);
            }
        }

        // متغير لتتبع التبويب النشط في المدفوعات
        let activePaymentTab = 'suppliers';

        // عرض وحدة المدفوعات
        function showPayments() {
            // حساب إحصائيات مدفوعات الموردين
            const supplierPaid = mockData.payments.filter(p => p.status === 'مدفوع').reduce((sum, p) => sum + p.amount, 0);
            const supplierPending = mockData.payments.filter(p => p.status === 'معلق').reduce((sum, p) => sum + p.amount, 0);
            const supplierOverdue = mockData.payments.filter(p => p.status === 'متأخر').length;
            const totalSupplierPayments = mockData.payments.reduce((sum, p) => sum + p.amount, 0);

            // حساب إحصائيات المدفوعات الأخرى
            const otherPaid = mockData.otherPayments.filter(p => p.status === 'مدفوع').reduce((sum, p) => sum + p.amount, 0);
            const otherPending = mockData.otherPayments.filter(p => p.status === 'معلق').reduce((sum, p) => sum + p.amount, 0);
            const otherOverdue = mockData.otherPayments.filter(p => p.status === 'متأخر').length;
            const totalOtherPayments = mockData.otherPayments.reduce((sum, p) => sum + p.amount, 0);

            // الإجماليات
            const totalPaid = supplierPaid + otherPaid;
            const totalPending = supplierPending + otherPending;
            const overdueCount = supplierOverdue + otherOverdue;
            const totalPayments = totalSupplierPayments + totalOtherPayments;

            return `
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold">إدارة المدفوعات</h1>
                            <p class="text-gray-600">تتبع وإدارة جميع المدفوعات والالتزامات المالية</p>
                        </div>
                        <div class="flex gap-2">
                            <button onclick="addNewItem('payment')" class="btn bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
                                ➕ دفعة للموردين
                            </button>
                            <button onclick="addNewItem('otherPayment')" class="btn bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2">
                                ➕ دفعة أخرى
                            </button>
                        </div>
                    </div>

                    <!-- تبويبات المدفوعات -->
                    <div class="border-b border-gray-200">
                        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                            <button onclick="showPaymentTab('suppliers')" id="suppliers-tab" class="payment-tab active border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-600">
                                🏢 مدفوعات الموردين
                            </button>
                            <button onclick="showPaymentTab('others')" id="others-tab" class="payment-tab border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                💼 مدفوعات أخرى
                            </button>
                        </nav>
                    </div>

                    <!-- بطاقات الإحصائيات -->
                    <div class="grid gap-4 md:grid-cols-4">
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي المدفوع</p>
                                    <p class="text-2xl font-bold">${totalPaid.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">هذا الشهر</p>
                                </div>
                                <div class="text-2xl">💳</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">مدفوعات معلقة</p>
                                    <p class="text-2xl font-bold">${totalPending.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">في الانتظار</p>
                                </div>
                                <div class="text-2xl">⏳</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">مدفوعات متأخرة</p>
                                    <p class="text-2xl font-bold text-red-600">${overdueCount}</p>
                                    <p class="text-xs text-gray-500">تحتاج متابعة</p>
                                </div>
                                <div class="text-2xl">⚠️</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">متوسط الدفعة</p>
                                    <p class="text-2xl font-bold">${Math.round(totalPayments / mockData.payments.length).toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">للدفعة الواحدة</p>
                                </div>
                                <div class="text-2xl">📊</div>
                            </div>
                        </div>
                    </div>

                    <!-- المدفوعات حسب الفئة -->
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <h3 class="text-lg font-semibold">المدفوعات حسب الفئة</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                                ${['موردين', 'مرافق', 'رواتب', 'تأمين', 'خدمات قانونية'].map(category => {
                                    const categoryPayments = mockData.payments.filter(p => p.category === category);
                                    const categoryTotal = categoryPayments.reduce((sum, p) => sum + p.amount, 0);
                                    if (categoryPayments.length === 0) return '';
                                    return `
                                        <div class="flex items-center justify-between p-3 border rounded-lg">
                                            <div>
                                                <p class="font-medium">${category}</p>
                                                <p class="text-sm text-gray-500">${categoryPayments.length} دفعة</p>
                                            </div>
                                            <div class="text-left">
                                                <p class="font-bold">${categoryTotal.toLocaleString()} ر.س</p>
                                            </div>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                    </div>

                    <!-- محتوى التبويبات -->
                    <div id="payment-tabs-content">
                        ${getPaymentTabContent(activePaymentTab)}
                    </div>


                    <!-- تنبيهات المدفوعات المتأخرة -->
                    ${mockData.payments.filter(p => p.status === 'متأخر').length > 0 ? `
                        <div class="rounded-lg border border-red-200 bg-red-50 p-6">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="text-2xl">⚠️</div>
                                <div>
                                    <h3 class="text-lg font-semibold text-red-800">تنبيه: مدفوعات متأخرة</h3>
                                    <p class="text-sm text-red-600">يوجد ${mockData.payments.filter(p => p.status === 'متأخر').length} مدفوعات متأخرة تحتاج إلى متابعة فورية</p>
                                </div>
                            </div>
                            <div class="space-y-2">
                                ${mockData.payments.filter(p => p.status === 'متأخر').map(payment => `
                                    <div class="flex items-center justify-between p-3 bg-white rounded border">
                                        <div>
                                            <p class="font-medium">${payment.recipient}</p>
                                            <p class="text-sm text-gray-600">${payment.description}</p>
                                        </div>
                                        <div class="text-left">
                                            <p class="font-bold text-red-600">${payment.amount.toLocaleString()} ر.س</p>
                                            <p class="text-xs text-gray-500">مستحق: ${payment.dueDate}</p>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            `;
        }



        // تبديل الثيم
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('theme-icon');

            if (body.classList.contains('dark')) {
                body.classList.remove('dark');
                themeIcon.textContent = '🌙';
            } else {
                body.classList.add('dark');
                themeIcon.textContent = '☀️';
            }
        }

        // وظائف تفاعلية للأزرار
        function addNewItem(type) {
            console.log('🚀 بدء إضافة عنصر جديد من النوع:', type);

            try {
                // التحقق من وجود البيانات الأساسية
                console.log('📊 فحص البيانات الأساسية...');
                console.log('mockData موجود:', !!mockData);
                console.log('mockData.suppliers موجود:', !!mockData.suppliers);
                console.log('عدد الموردين:', mockData.suppliers ? mockData.suppliers.length : 0);

                // تحديث قائمة الموردين ديناميكياً
                const supplierNames = mockData.suppliers ? mockData.suppliers.map(s => s.name) : ['لا توجد موردين'];
                console.log('📝 أسماء الموردين:', supplierNames);

                const forms = {
                purchase: {
                    title: 'إضافة طلب شراء جديد',
                    fields: [
                        { name: 'supplier', label: 'المورد *', type: 'select', options: mockData.suppliers.map(s => s.name), required: true, onchange: 'handleSupplierChange(this)' },
                        { name: 'invoice', label: 'رقم الفاتورة', type: 'text', readonly: true, placeholder: 'سيتم التوليد تلقائياً عند اختيار المورد', id: 'invoiceNumber' },
                        { name: 'amount', label: 'المبلغ *', type: 'number', required: true, step: '0.01', min: '0' },
                        { name: 'items', label: 'عدد الأصناف *', type: 'number', required: true, min: '1' },
                        { name: 'description', label: 'الوصف', type: 'textarea', placeholder: 'وصف تفصيلي للمشتريات...' }
                    ]
                },
                payment: {
                    title: 'إضافة دفعة للموردين',
                    fields: [
                        { name: 'recipient', label: 'اسم المورد', type: 'select', options: supplierNames, required: true },
                        { name: 'amount', label: 'المبلغ', type: 'number', required: true },
                        { name: 'method', label: 'طريقة الدفع', type: 'select', options: ['نقدي', 'تحويل بنكي', 'شيك', 'بطاقة'], required: true },
                        { name: 'description', label: 'الوصف', type: 'textarea', required: true }
                    ]
                },
                otherPayment: {
                    title: 'إضافة دفعة أخرى',
                    fields: [
                        { name: 'recipient', label: 'المستفيد', type: 'text', required: true },
                        { name: 'amount', label: 'المبلغ', type: 'number', required: true },
                        { name: 'method', label: 'طريقة الدفع', type: 'select', options: ['نقدي', 'تحويل بنكي', 'شيك', 'بطاقة'], required: true },
                        { name: 'category', label: 'الفئة', type: 'select', options: ['مرافق', 'رواتب', 'تأمين', 'تعاقدات', 'تسويق', 'خدمات قانونية', 'استشارات'], required: true },
                        { name: 'description', label: 'الوصف', type: 'textarea', required: true }
                    ]
                },
                supplier: {
                    title: 'إضافة مورد جديد',
                    fields: [
                        { name: 'name', label: 'اسم الشركة', type: 'text', required: true },
                        { name: 'contact', label: 'الشخص المسؤول', type: 'text', required: true },
                        { name: 'phone', label: 'رقم الهاتف', type: 'tel', required: true },
                        { name: 'email', label: 'البريد الإلكتروني', type: 'email', required: true },
                        { name: 'city', label: 'المدينة', type: 'text', required: false },
                        { name: 'previousBalance', label: 'الرصيد السابق', type: 'number', required: false }
                    ]
                },
                employee: {
                    title: 'إضافة موظف جديد',
                    fields: [
                        { name: 'name', label: 'الاسم الكامل', type: 'text', required: true },
                        { name: 'position', label: 'المنصب', type: 'text', required: true },
                        { name: 'department', label: 'القسم', type: 'select', options: ['المبيعات', 'المالية', 'التقنية', 'الموارد البشرية'], required: true },
                        { name: 'phone', label: 'رقم الهاتف', type: 'tel', required: false },
                        { name: 'email', label: 'البريد الإلكتروني', type: 'email', required: false },
                        { name: 'salary', label: 'الراتب', type: 'number', required: true }
                    ]
                },
                expense: {
                    title: 'إضافة مصروف جديد',
                    fields: [
                        { name: 'description', label: 'الوصف', type: 'text' },
                        { name: 'amount', label: 'المبلغ', type: 'number' },
                        { name: 'category', label: 'الفئة', type: 'select', options: ['مرافق', 'مواصلات', 'مكتب', 'تقنية'] },
                        { name: 'method', label: 'طريقة الدفع', type: 'select', options: ['نقدي', 'تحويل بنكي', 'شيك', 'بطاقة'] }
                    ]
                },
                sale: {
                    title: 'إضافة مبيعة جديدة',
                    fields: [
                        { name: 'customer', label: 'اسم العميل', type: 'text' },
                        { name: 'phone', label: 'رقم الهاتف', type: 'tel' },
                        { name: 'total', label: 'المبلغ الإجمالي', type: 'number' },
                        { name: 'method', label: 'طريقة الدفع', type: 'select', options: ['نقدي', 'بطاقة', 'تحويل بنكي', 'تقسيط'] },
                        { name: 'source', label: 'المصدر', type: 'select', options: ['في المتجر', 'تطبيق توصيل', 'متجر إلكتروني'] }
                    ]
                }
            };

                console.log('📋 النماذج المتاحة:', Object.keys(forms));

                const form = forms[type];
                console.log('🔍 البحث عن نموذج النوع:', type);
                console.log('📝 النموذج الموجود:', !!form);

                if (!form) {
                    console.error('❌ لم يتم العثور على نموذج للنوع:', type);
                    showNotification(`نوع غير مدعوم: ${type} ❌`, 'error');
                    return;
                }

                console.log('✅ تم العثور على النموذج:', form.title);
                console.log('📊 عدد الحقول:', form.fields.length);

                console.log('🎨 بدء إنشاء النموذج...');
                const formHTML = createForm(form.fields, type);
                console.log('✅ تم إنشاء HTML النموذج بنجاح');

                console.log('🪟 عرض النافذة المنبثقة...');
                showModal(form.title, formHTML, type);
                console.log('✅ تم عرض النافذة المنبثقة بنجاح');

            } catch (error) {
                console.error('💥 خطأ في addNewItem:', error);
                console.error('📍 تفاصيل الخطأ:', error.message);
                console.error('📚 مكان الخطأ:', error.stack);
                showNotification(`خطأ في فتح النموذج: ${error.message} ❌`, 'error');
            }
        }

        function createEditForm(fields, type, id) {
            return `
                <form id="editForm" class="space-y-4">
                    <input type="hidden" name="id" value="${id}">
                    <input type="hidden" name="type" value="${type}">
                    ${fields.map(field => {
                        if (field.type === 'select') {
                            return `
                                <div>
                                    <label class="block text-sm font-medium mb-2">${field.label}</label>
                                    <select name="${field.name}" class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none">
                                        ${field.options.map(option => `
                                            <option value="${option}" ${option === field.value ? 'selected' : ''}>${option}</option>
                                        `).join('')}
                                    </select>
                                </div>
                            `;
                        } else if (field.type === 'textarea') {
                            return `
                                <div>
                                    <label class="block text-sm font-medium mb-2">${field.label}</label>
                                    <textarea name="${field.name}" rows="3" class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none" placeholder="أدخل ${field.label}">${field.value || ''}</textarea>
                                </div>
                            `;
                        } else {
                            const extraAttrs = field.min ? `min="${field.min}"` : '';
                            const extraAttrs2 = field.max ? `max="${field.max}"` : '';
                            const extraAttrs3 = field.step ? `step="${field.step}"` : '';
                            return `
                                <div>
                                    <label class="block text-sm font-medium mb-2">${field.label}</label>
                                    <input type="${field.type}" name="${field.name}" value="${field.value || ''}"
                                           class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none"
                                           placeholder="أدخل ${field.label}" ${extraAttrs} ${extraAttrs2} ${extraAttrs3}>
                                </div>
                            `;
                        }
                    }).join('')}
                    <div class="flex gap-3 pt-4">
                        <button type="submit" class="btn flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                            💾 حفظ التعديلات
                        </button>
                        <button type="button" onclick="closeModal()" class="btn flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400">
                            ❌ إلغاء
                        </button>
                    </div>
                </form>
            `;
        }

        function createForm(fields, type) {
            try {
                console.log('🎨 بدء إنشاء نموذج للنوع:', type);
                console.log('📊 الحقول المرسلة:', fields);
                console.log('📝 عدد الحقول:', fields ? fields.length : 0);

                return `
                    <form id="itemForm" class="space-y-4">
                        ${fields.map(field => {
                            try {
                                const requiredMark = field.required ? '<span class="text-red-500">*</span>' : '';
                                const requiredAttr = field.required ? 'required' : '';

                                if (field.type === 'select') {
                                    const options = field.options || [];
                                    console.log(`خيارات الحقل ${field.name}:`, options);

                                    const onchangeAttr = field.onchange ? `onchange="${field.onchange}"` : '';
                                    const idAttr = field.id ? `id="${field.id}"` : '';

                                    return `
                                        <div>
                                            <label class="block text-sm font-medium mb-2">${field.label} ${requiredMark}</label>
                                            <select name="${field.name}" ${idAttr} class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none" ${requiredAttr} ${onchangeAttr}>
                                                <option value="">اختر ${field.label}</option>
                                                ${options.map(option => `<option value="${option}">${option}</option>`).join('')}
                                            </select>
                                        </div>
                                    `;
                                } else if (field.type === 'textarea') {
                                    const placeholderAttr = field.placeholder ? `placeholder="${field.placeholder}"` : `placeholder="أدخل ${field.label}"`;
                                    const idAttr = field.id ? `id="${field.id}"` : '';

                                    return `
                                        <div>
                                            <label class="block text-sm font-medium mb-2">${field.label} ${requiredMark}</label>
                                            <textarea name="${field.name}" ${idAttr} rows="3" class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none" ${placeholderAttr} ${requiredAttr}></textarea>
                                        </div>
                                    `;
                                } else {
                                    const placeholderAttr = field.placeholder ? `placeholder="${field.placeholder}"` : `placeholder="أدخل ${field.label}"`;
                                    const readonlyAttr = field.readonly ? 'readonly' : '';
                                    const stepAttr = field.step ? `step="${field.step}"` : '';
                                    const minAttr = field.min ? `min="${field.min}"` : '';
                                    const maxAttr = field.max ? `max="${field.max}"` : '';
                                    const idAttr = field.id ? `id="${field.id}"` : '';
                                    const onchangeAttr = field.onchange ? `onchange="${field.onchange}"` : '';

                                    const readonlyClass = field.readonly ? 'bg-gray-100 cursor-not-allowed' : '';

                                    return `
                                        <div>
                                            <label class="block text-sm font-medium mb-2">${field.label} ${requiredMark}</label>
                                            <input type="${field.type}" name="${field.name}" ${idAttr} class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none ${readonlyClass}" ${placeholderAttr} ${requiredAttr} ${readonlyAttr} ${stepAttr} ${minAttr} ${maxAttr} ${onchangeAttr}>
                                            ${field.readonly ? '<small class="text-gray-500 text-xs mt-1 block">هذا الحقل يتم ملؤه تلقائياً</small>' : ''}
                                        </div>
                                    `;
                                }
                            } catch (fieldError) {
                                console.error('خطأ في إنشاء الحقل:', field.name, fieldError);
                                return `<div class="text-red-500">خطأ في الحقل: ${field.name}</div>`;
                            }
                        }).join('')}
                        <div class="flex gap-3 pt-4">
                            <button type="submit" class="btn flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                                💾 حفظ
                            </button>
                            <button type="button" onclick="closeModal()" class="btn flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400">
                                ❌ إلغاء
                            </button>
                        </div>
                    </form>
                `;
            } catch (error) {
                console.error('خطأ في إنشاء النموذج:', error);
                return `<div class="text-red-500">خطأ في إنشاء النموذج: ${error.message}</div>`;
            }
        }

        function showEditModal(title, content, type, id) {
            const modal = `
                <div id="modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onclick="closeModal()">
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto" onclick="event.stopPropagation()">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold">${title}</h3>
                            <button onclick="closeModal()" class="icon-btn text-gray-500 hover:text-gray-700">❌</button>
                        </div>
                        ${content}
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modal);

            // إضافة مستمع للنموذج
            document.getElementById('editForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(e.target);
                const data = Object.fromEntries(formData);

                // تحديث البيانات في المصفوفة المناسبة
                updateItemData(type, id, data);

                showNotification('تم تحديث البيانات بنجاح! ✅', 'success');
                closeModal();

                // إعادة تحميل القسم لإظهار التحديثات
                forceReloadSection(getCurrentSection());
            });
        }

        function showModal(title, content, type, size = 'normal') {
            try {
                console.log('🪟 بدء عرض النافذة المنبثقة:', title);
                console.log('📄 طول المحتوى:', content ? content.length : 0);
                console.log('🏷️ نوع العنصر:', type);
                console.log('📏 حجم النافذة:', size);

                // إزالة أي نافذة منبثقة موجودة
                const existingModal = document.getElementById('modal');
                if (existingModal) {
                    existingModal.remove();
                    console.log('تم إزالة النافذة المنبثقة السابقة');
                }

                // تحديد حجم النافذة
                let modalSize = 'max-w-md';
                let modalHeight = '';

                switch(size) {
                    case 'large':
                        modalSize = 'max-w-4xl';
                        modalHeight = 'max-h-[90vh] overflow-y-auto';
                        break;
                    case 'extra-large':
                        modalSize = 'max-w-6xl';
                        modalHeight = 'max-h-[95vh] overflow-y-auto';
                        break;
                    case 'full':
                        modalSize = 'max-w-[95vw]';
                        modalHeight = 'max-h-[95vh] overflow-y-auto';
                        break;
                    default:
                        modalSize = 'max-w-md';
                }

                const modal = `
                    <div id="modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" onclick="closeModal()">
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-2xl w-full ${modalSize} ${modalHeight} mx-4" onclick="event.stopPropagation()">
                            <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-t-lg">
                                <h3 class="text-xl font-bold">${title}</h3>
                                <button onclick="closeModal()" class="text-white hover:text-gray-200 text-2xl font-bold transition-colors">×</button>
                            </div>
                            <div class="p-6">
                                ${content}
                            </div>
                        </div>
                    </div>
                `;
                document.body.insertAdjacentHTML('beforeend', modal);
                console.log('تم عرض النافذة المنبثقة بنجاح');

            // إضافة مستمع للنموذج مع تأخير قصير (فقط إذا كان type موجود)
            if (!type) {
                console.log('ℹ️ لا يوجد نوع محدد - تخطي إضافة مستمع النموذج');
                return;
            }

            setTimeout(() => {
                try {
                    console.log('🔍 البحث عن النموذج في DOM...');
                    const form = document.getElementById('itemForm');

                    if (!form) {
                        console.error('❌ لم يتم العثور على النموذج في DOM!');
                        console.log('🔍 عناصر DOM الموجودة:', document.querySelectorAll('form'));
                        showNotification('خطأ في تحميل النموذج! ❌', 'error');
                        return;
                    }

                    console.log('✅ تم العثور على النموذج بنجاح');
                    console.log('📋 عناصر النموذج:', form.elements.length);
                    console.log('🏷️ نوع العنصر المؤكد:', type);

            form.addEventListener('submit', function(e) {
                e.preventDefault();
                console.log('📤 تم إرسال النموذج - بدء المعالجة');

                try {
                    console.log('📊 استخراج البيانات من النموذج...');
                    const formData = new FormData(e.target);
                    const data = Object.fromEntries(formData);

                    console.log('📋 بيانات النموذج الخام:', formData);
                    console.log('🔄 بيانات النموذج المحولة:', data);
                    console.log('🏷️ نوع العنصر:', type);
                    console.log('📝 عدد الحقول المرسلة:', Object.keys(data).length);

                    // التحقق من وجود البيانات
                    if (Object.keys(data).length === 0) {
                        console.error('لا توجد بيانات في النموذج!');
                        showNotification('لا توجد بيانات للحفظ! ❌', 'error');
                        return;
                    }

                    // التحقق من صحة البيانات
                    console.log('✅ بدء التحقق من صحة البيانات...');
                    const isValid = validateFormData(data, type);
                    console.log('📊 نتيجة التحقق:', isValid);

                    if (!isValid) {
                        console.log('❌ فشل التحقق من البيانات');
                        return; // validateFormData تعرض الرسالة بنفسها
                    }

                    console.log('✅ تم التحقق من البيانات بنجاح');

                    console.log('💾 بدء عملية الحفظ...');
                    console.log('📤 إرسال البيانات للحفظ:', { type, data });

                    // إضافة العنصر الجديد للبيانات
                    console.log('🔄 استدعاء دالة addNewItemToData...');
                    const success = addNewItemToData(type, data);
                    console.log('📊 نتيجة عملية الحفظ:', success);

                    if (success) {
                        console.log('تم الحفظ بنجاح');
                        showNotification('تم حفظ البيانات بنجاح! ✅', 'success');
                        closeModal();

                        // إعادة تحميل القسم فوراً
                        const currentSection = getCurrentSection();
                        console.log('إعادة تحميل القسم:', currentSection);

                        // إعادة تحميل بقوة
                        setTimeout(() => {
                            forceReloadSection(currentSection);
                        }, 100);
                    } else {
                        console.error('فشل في الحفظ');
                        showNotification('حدث خطأ أثناء حفظ البيانات! ❌', 'error');
                    }
                } catch (error) {
                    console.error('خطأ في معالجة النموذج:', error);
                    console.error('تفاصيل الخطأ:', error.message);
                    console.error('مكان الخطأ:', error.stack);
                    showNotification(`حدث خطأ غير متوقع: ${error.message} ❌`, 'error');
                }
            });
                } catch (modalError) {
                    console.error('خطأ في إعداد النافذة المنبثقة:', modalError);
                    showNotification('خطأ في تحميل النموذج! ❌', 'error');
                }
            }, 100);
            } catch (error) {
                console.error('خطأ في عرض النافذة المنبثقة:', error);
                showNotification('خطأ في عرض النموذج! ❌', 'error');
            }
        }

        function closeModal() {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.remove();
            }
        }

        function showNotification(message, type = 'info') {
            const colors = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                warning: 'bg-yellow-500',
                info: 'bg-blue-500'
            };

            const notification = `
                <div id="notification" class="fixed top-4 right-4 ${colors[type]} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300">
                    ${message}
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', notification);

            const notif = document.getElementById('notification');
            setTimeout(() => notif.classList.remove('translate-x-full'), 100);
            setTimeout(() => {
                notif.classList.add('translate-x-full');
                setTimeout(() => notif.remove(), 300);
            }, 3000);
        }

        function viewItem(type, id) {
            const items = {
                purchase: mockData.purchases.find(p => p.id == id),
                payment: mockData.payments.find(p => p.id == id),
                supplier: mockData.suppliers.find(s => s.id == id),
                employee: mockData.employees.find(e => e.id == id),
                expense: mockData.expenses.find(e => e.id == id),
                sale: mockData.sales.find(s => s.id == id)
            };

            const item = items[type];
            if (!item) return;

            // إذا كان المورد، تحديث بياناته أولاً
            if (type === 'supplier') {
                updateSuppliersData();
                const updatedItem = mockData.suppliers.find(s => s.id == id);
                if (updatedItem) {
                    Object.assign(item, updatedItem);
                }
            }

            // إضافة قسم المشتريات والمدفوعات للموردين
            let purchasesSection = '';
            let paymentsSection = '';

            if (type === 'supplier') {
                const supplierPurchases = mockData.purchases.filter(p => p.supplier_id == id);
                const supplierPayments = mockData.payments.filter(p => p.supplier_id == id);

                if (supplierPurchases.length > 0) {
                    const totalPurchasesAmount = supplierPurchases.reduce((sum, p) => sum + p.amount, 0);
                    const totalItems = supplierPurchases.reduce((sum, p) => sum + p.items, 0);

                    purchasesSection = `
                        <div class="overflow-x-auto">
                            <table class="w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm">
                                <thead>
                                    <tr class="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                                        <th class="text-right p-3 font-medium">رقم الفاتورة</th>
                                        <th class="text-right p-3 font-medium">التاريخ</th>
                                        <th class="text-right p-3 font-medium">المبلغ</th>
                                        <th class="text-right p-3 font-medium">الأصناف</th>
                                        <th class="text-right p-3 font-medium">الحالة</th>
                                        <th class="text-right p-3 font-medium">الوصف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${supplierPurchases.map((purchase, index) => `
                                        <tr class="${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'} hover:bg-blue-50 transition-colors">
                                            <td class="p-3 font-medium text-blue-600">${purchase.invoice}</td>
                                            <td class="p-3 text-gray-600">${purchase.date}</td>
                                            <td class="p-3 font-semibold text-green-600">${purchase.amount.toLocaleString()} ر.س</td>
                                            <td class="p-3 text-center">${purchase.items}</td>
                                            <td class="p-3">
                                                <span class="px-2 py-1 rounded-full text-xs font-medium ${
                                                    purchase.status === 'مستلم' ? 'bg-green-100 text-green-800' :
                                                    purchase.status === 'معتمد' ? 'bg-blue-100 text-blue-800' :
                                                    'bg-yellow-100 text-yellow-800'
                                                }">
                                                    ${purchase.status}
                                                </span>
                                            </td>
                                            <td class="p-3 text-gray-600 text-sm">${purchase.description || '-'}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                                <tfoot>
                                    <tr class="bg-gradient-to-r from-gray-100 to-gray-200 font-bold">
                                        <td class="p-3 text-gray-800">الإجمالي</td>
                                        <td class="p-3 text-gray-600">${supplierPurchases.length} مشترى</td>
                                        <td class="p-3 text-green-700 font-bold">${totalPurchasesAmount.toLocaleString()} ر.س</td>
                                        <td class="p-3 text-center font-bold">${totalItems}</td>
                                        <td colspan="2" class="p-3"></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    `;
                } else {
                    purchasesSection = `
                        <div class="text-center py-8 text-gray-500">
                            <div class="text-4xl mb-3">📦</div>
                            <p class="text-lg font-medium">لا توجد مشتريات</p>
                            <p class="text-sm">لم يتم تسجيل أي مشتريات لهذا المورد بعد</p>
                        </div>
                    `;
                }

                // قسم المدفوعات
                if (supplierPayments.length > 0) {
                    const totalPaymentsAmount = supplierPayments.reduce((sum, p) => sum + p.amount, 0);

                    paymentsSection = `
                        <div class="overflow-x-auto">
                            <table class="w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm">
                                <thead>
                                    <tr class="bg-gradient-to-r from-green-500 to-green-600 text-white">
                                        <th class="text-right p-3 font-medium">رقم المرجع</th>
                                        <th class="text-right p-3 font-medium">التاريخ</th>
                                        <th class="text-right p-3 font-medium">المبلغ</th>
                                        <th class="text-right p-3 font-medium">طريقة الدفع</th>
                                        <th class="text-right p-3 font-medium">الحالة</th>
                                        <th class="text-right p-3 font-medium">الوصف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${supplierPayments.map((payment, index) => `
                                        <tr class="${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'} hover:bg-green-50 transition-colors">
                                            <td class="p-3 font-medium text-green-600">${payment.reference || '-'}</td>
                                            <td class="p-3 text-gray-600">${payment.date}</td>
                                            <td class="p-3 font-semibold text-green-600">${payment.amount.toLocaleString()} ر.س</td>
                                            <td class="p-3 text-gray-600">${payment.method}</td>
                                            <td class="p-3">
                                                <span class="px-2 py-1 rounded-full text-xs font-medium ${
                                                    payment.status === 'مدفوع' ? 'bg-green-100 text-green-800' :
                                                    payment.status === 'معلق' ? 'bg-yellow-100 text-yellow-800' :
                                                    'bg-red-100 text-red-800'
                                                }">
                                                    ${payment.status}
                                                </span>
                                            </td>
                                            <td class="p-3 text-gray-600 text-sm">${payment.description || '-'}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                                <tfoot>
                                    <tr class="bg-gradient-to-r from-gray-100 to-gray-200 font-bold">
                                        <td class="p-3 text-gray-800">الإجمالي</td>
                                        <td class="p-3 text-gray-600">${supplierPayments.length} دفعة</td>
                                        <td class="p-3 text-green-700 font-bold">${totalPaymentsAmount.toLocaleString()} ر.س</td>
                                        <td colspan="3" class="p-3"></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    `;
                } else {
                    paymentsSection = `
                        <div class="text-center py-8 text-gray-500">
                            <div class="text-4xl mb-3">💳</div>
                            <p class="text-lg font-medium">لا توجد مدفوعات</p>
                            <p class="text-sm">لم يتم تسجيل أي مدفوعات لهذا المورد بعد</p>
                        </div>
                    `;
                }
            } else {
                // للأنواع الأخرى، تأكد من وجود قيم فارغة
                purchasesSection = '';
                paymentsSection = '';
            }

            // إنشاء محتوى احترافي للنافذة
            const content = createSupplierDetailView(item, type, purchasesSection, paymentsSection);

            showModal(`تفاصيل ${getTypeLabel(type)}`, content, null, 'extra-large');
        }

        // دالة إنشاء عرض تفاصيل المورد الاحترافي
        function createSupplierDetailView(item, type, purchasesSection, paymentsSection) {
            // تعيين قيم افتراضية
            purchasesSection = purchasesSection || '';
            paymentsSection = paymentsSection || '';

            if (type !== 'supplier') {
                // للأنواع الأخرى، استخدم العرض البسيط
                return `
                    <div class="space-y-4">
                        ${Object.entries(item).map(([key, value]) => {
                            const label = getFieldLabel(key);
                            let displayValue = value;

                            if (key === 'amount' || key === 'total' || key === 'salary') {
                                displayValue = `${value.toLocaleString()} ر.س`;
                            } else if (key === 'rating') {
                                displayValue = `${value} ⭐`;
                            }

                            return `
                                <div class="flex justify-between py-3 border-b border-gray-100">
                                    <span class="font-medium text-gray-600">${label}:</span>
                                    <span class="text-gray-900">${displayValue}</span>
                                </div>
                            `;
                        }).join('')}

                        <div class="flex gap-3 pt-4 mt-6 border-t">
                            <button onclick="printSupplierDetails(${item.id})" class="btn flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center justify-center gap-2">
                                🖨️ طباعة
                            </button>
                            <button onclick="exportSupplierToPDF(${item.id})" class="btn flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 flex items-center justify-center gap-2">
                                📄 تصدير PDF
                            </button>
                            <button onclick="closeModal()" class="btn flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400">
                                ❌ إغلاق
                            </button>
                        </div>
                    </div>
                `;
            }

            // عرض احترافي خاص بالموردين
            return `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- المعلومات الأساسية -->
                    <div class="lg:col-span-2 space-y-6">
                        <!-- بطاقة المعلومات الشخصية -->
                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
                            <h4 class="text-lg font-bold text-blue-800 mb-4 flex items-center gap-2">
                                👤 المعلومات الأساسية
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="space-y-3">
                                    <div class="flex items-center gap-3">
                                        <span class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-bold text-sm">🏢</span>
                                        <div>
                                            <p class="text-sm text-gray-600">اسم الشركة</p>
                                            <p class="font-semibold text-gray-900">${item.name}</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-3">
                                        <span class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-green-600 font-bold text-sm">👨</span>
                                        <div>
                                            <p class="text-sm text-gray-600">الشخص المسؤول</p>
                                            <p class="font-semibold text-gray-900">${item.contact}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="space-y-3">
                                    <div class="flex items-center gap-3">
                                        <span class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 font-bold text-sm">📞</span>
                                        <div>
                                            <p class="text-sm text-gray-600">رقم الهاتف</p>
                                            <p class="font-semibold text-gray-900 direction-ltr">${item.phone}</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-3">
                                        <span class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center text-orange-600 font-bold text-sm">📧</span>
                                        <div>
                                            <p class="text-sm text-gray-600">البريد الإلكتروني</p>
                                            <p class="font-semibold text-gray-900 direction-ltr">${item.email}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-4 pt-4 border-t border-blue-200">
                                <div class="flex items-center gap-3">
                                    <span class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center text-yellow-600 font-bold text-sm">📍</span>
                                    <div>
                                        <p class="text-sm text-gray-600">المدينة</p>
                                        <p class="font-semibold text-gray-900">${item.city}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- المعلومات المالية -->
                        <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6 border border-green-200">
                            <h4 class="text-lg font-bold text-green-800 mb-4 flex items-center gap-2">
                                💰 المعلومات المالية
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="bg-white rounded-lg p-4 border border-green-100">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-sm text-gray-600">إجمالي المشتريات</p>
                                            <p class="text-2xl font-bold text-green-600">${item.totalPurchases.toLocaleString()} ر.س</p>
                                            <p class="text-xs text-gray-500">عدد المشتريات: ${item.purchaseCount || 0}</p>
                                        </div>
                                        <div class="text-3xl">📈</div>
                                    </div>
                                </div>
                                <div class="bg-white rounded-lg p-4 border border-blue-100">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-sm text-gray-600">إجمالي المدفوعات</p>
                                            <p class="text-2xl font-bold text-blue-600">${(item.totalPayments || 0).toLocaleString()} ر.س</p>
                                            <p class="text-xs text-gray-500">عدد المدفوعات: ${item.paymentsCount || 0}</p>
                                        </div>
                                        <div class="text-3xl">💳</div>
                                    </div>
                                </div>
                                <div class="bg-white rounded-lg p-4 border border-purple-100">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-sm text-gray-600">الرصيد السابق</p>
                                            <p class="text-2xl font-bold text-purple-600">${(item.previousBalance || 0).toLocaleString()} ر.س</p>
                                            <p class="text-xs text-gray-500">رصيد مرحل</p>
                                        </div>
                                        <div class="text-3xl">📊</div>
                                    </div>
                                </div>
                                <div class="bg-gradient-to-r from-orange-400 to-orange-500 rounded-lg p-4 text-white">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-orange-100">إجمالي المستحقات</p>
                                            <p class="text-2xl font-bold">${item.totalOutstanding.toLocaleString()} ر.س</p>
                                            <p class="text-xs text-orange-200">المبلغ المطلوب دفعه</p>
                                        </div>
                                        <div class="text-3xl">💰</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        ${purchasesSection ? `
                        <!-- قسم المشتريات -->
                        <div class="bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-6 border border-gray-200">
                            <h4 class="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                                📦 سجل المشتريات
                            </h4>
                            ${purchasesSection}
                        </div>
                        ` : ''}

                        ${paymentsSection ? `
                        <!-- قسم المدفوعات -->
                        <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6 border border-green-200">
                            <h4 class="text-lg font-bold text-green-800 mb-4 flex items-center gap-2">
                                💳 سجل المدفوعات
                            </h4>
                            ${paymentsSection}
                        </div>
                        ` : ''}
                    </div>

                    <!-- الشريط الجانبي -->
                    <div class="space-y-6">
                        <!-- معلومات إضافية -->
                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
                            <h4 class="text-lg font-bold text-blue-800 mb-4 flex items-center gap-2">
                                📋 معلومات إضافية
                            </h4>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">رقم المورد</span>
                                    <span class="font-medium">#${item.id}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">تاريخ التسجيل</span>
                                    <span class="font-medium">2024-01-01</span>
                                </div>
                            </div>
                        </div>

                        <!-- إحصائيات سريعة -->
                        <div class="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
                            <h4 class="text-lg font-bold text-gray-800 mb-4">📊 إحصائيات سريعة</h4>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">آخر تاريخ شراء</span>
                                    <span class="font-medium">${item.lastPurchaseDate || 'لا يوجد'}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">آخر تاريخ دفعة</span>
                                    <span class="font-medium">${item.lastPaymentDate || 'لا يوجد'}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">إجمالي المدفوعات</span>
                                    <span class="font-medium text-green-600">${(item.totalPayments || 0).toLocaleString()} ر.س</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">متوسط قيمة الشراء</span>
                                    <span class="font-medium">${item.purchaseCount > 0 ? Math.round(item.totalPurchases / item.purchaseCount).toLocaleString() : 0} ر.س</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">حالة الحساب</span>
                                    <span class="px-2 py-1 rounded-full text-xs font-medium ${item.balance > 0 ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'}">
                                        ${item.balance > 0 ? 'مديون' : 'مسدد'}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار العمليات -->
                        <div class="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
                            <h4 class="text-lg font-bold text-gray-800 mb-4">⚡ العمليات</h4>
                            <div class="space-y-3">
                                <button onclick="printSupplierDetails(${item.id})" class="w-full bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 flex items-center justify-center gap-2 transition-colors">
                                    🖨️ طباعة التفاصيل
                                </button>
                                <button onclick="exportSupplierToPDF(${item.id})" class="w-full bg-red-600 text-white px-4 py-3 rounded-lg hover:bg-red-700 flex items-center justify-center gap-2 transition-colors">
                                    📄 تصدير PDF
                                </button>
                                <button onclick="editItem('supplier', ${item.id})" class="w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 flex items-center justify-center gap-2 transition-colors">
                                    ✏️ تعديل البيانات
                                </button>
                                <button onclick="closeModal()" class="w-full bg-gray-300 text-gray-700 px-4 py-3 rounded-lg hover:bg-gray-400 transition-colors">
                                    ❌ إغلاق
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function editItem(type, id) {
            const items = {
                purchase: mockData.purchases.find(p => p.id == id),
                payment: mockData.payments.find(p => p.id == id),
                supplier: mockData.suppliers.find(s => s.id == id),
                employee: mockData.employees.find(e => e.id == id),
                expense: mockData.expenses.find(e => e.id == id),
                sale: mockData.sales.find(s => s.id == id)
            };

            const item = items[type];
            if (!item) {
                showNotification('لم يتم العثور على العنصر! ❌', 'error');
                return;
            }

            const forms = {
                purchase: {
                    title: 'تعديل طلب الشراء',
                    fields: [
                        { name: 'supplier', label: 'المورد', type: 'select', options: mockData.suppliers.map(s => s.name), value: item.supplier },
                        { name: 'invoice', label: 'رقم الفاتورة', type: 'text', value: item.invoice },
                        { name: 'amount', label: 'المبلغ', type: 'number', value: item.amount },
                        { name: 'items', label: 'عدد الأصناف', type: 'number', value: item.items },
                        { name: 'status', label: 'الحالة', type: 'select', options: ['في الانتظار', 'معتمد', 'مستلم', 'ملغي'], value: item.status }
                    ]
                },
                payment: {
                    title: 'تعديل الدفعة',
                    fields: [
                        { name: 'recipient', label: 'المستفيد', type: 'text', value: item.recipient },
                        { name: 'amount', label: 'المبلغ', type: 'number', value: item.amount },
                        { name: 'method', label: 'طريقة الدفع', type: 'select', options: ['نقدي', 'تحويل بنكي', 'شيك', 'بطاقة'], value: item.method },
                        { name: 'category', label: 'الفئة', type: 'select', options: ['موردين', 'مرافق', 'رواتب', 'تأمين', 'خدمات قانونية'], value: item.category },
                        { name: 'status', label: 'الحالة', type: 'select', options: ['مدفوع', 'معلق', 'متأخر', 'ملغي'], value: item.status },
                        { name: 'description', label: 'الوصف', type: 'textarea', value: item.description }
                    ]
                },
                supplier: {
                    title: 'تعديل بيانات المورد',
                    fields: [
                        { name: 'name', label: 'اسم الشركة', type: 'text', value: item.name },
                        { name: 'contact', label: 'الشخص المسؤول', type: 'text', value: item.contact },
                        { name: 'phone', label: 'رقم الهاتف', type: 'tel', value: item.phone },
                        { name: 'email', label: 'البريد الإلكتروني', type: 'email', value: item.email },
                        { name: 'city', label: 'المدينة', type: 'text', value: item.city },
                        { name: 'previousBalance', label: 'المبلغ السابق', type: 'number', value: item.previousBalance || 0 }
                    ]
                },
                employee: {
                    title: 'تعديل بيانات الموظف',
                    fields: [
                        { name: 'name', label: 'الاسم الكامل', type: 'text', value: item.name },
                        { name: 'position', label: 'المنصب', type: 'text', value: item.position },
                        { name: 'department', label: 'القسم', type: 'select', options: ['المبيعات', 'المالية', 'التقنية', 'الموارد البشرية'], value: item.department },
                        { name: 'phone', label: 'رقم الهاتف', type: 'tel', value: item.phone },
                        { name: 'email', label: 'البريد الإلكتروني', type: 'email', value: item.email },
                        { name: 'salary', label: 'الراتب', type: 'number', value: item.salary },
                        { name: 'status', label: 'الحالة', type: 'select', options: ['نشط', 'غير نشط', 'في إجازة'], value: item.status }
                    ]
                },
                expense: {
                    title: 'تعديل المصروف',
                    fields: [
                        { name: 'description', label: 'الوصف', type: 'text', value: item.description },
                        { name: 'amount', label: 'المبلغ', type: 'number', value: item.amount },
                        { name: 'category', label: 'الفئة', type: 'select', options: ['مرافق', 'مواصلات', 'مكتب', 'تقنية'], value: item.category },
                        { name: 'method', label: 'طريقة الدفع', type: 'select', options: ['نقدي', 'تحويل بنكي', 'شيك', 'بطاقة'], value: item.method },
                        { name: 'status', label: 'الحالة', type: 'select', options: ['معتمد', 'في الانتظار', 'مرفوض'], value: item.status }
                    ]
                },
                sale: {
                    title: 'تعديل المبيعة',
                    fields: [
                        { name: 'customer', label: 'اسم العميل', type: 'text', value: item.customer },
                        { name: 'phone', label: 'رقم الهاتف', type: 'tel', value: item.phone || '' },
                        { name: 'total', label: 'المبلغ الإجمالي', type: 'number', value: item.total },
                        { name: 'method', label: 'طريقة الدفع', type: 'select', options: ['نقدي', 'بطاقة', 'تحويل بنكي', 'تقسيط'], value: item.method },
                        { name: 'source', label: 'المصدر', type: 'select', options: ['في المتجر', 'تطبيق توصيل', 'متجر إلكتروني'], value: item.source },
                        { name: 'status', label: 'الحالة', type: 'select', options: ['مكتمل', 'معلق', 'ملغي', 'مسترد'], value: item.status }
                    ]
                }
            };

            const form = forms[type];
            if (!form) return;

            showEditModal(form.title, createEditForm(form.fields, type, id), type, id);
        }

        function deleteItem(type, id) {
            if (confirm('هل أنت متأكد من حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.')) {
                const dataArrays = {
                    purchase: mockData.purchases,
                    payment: mockData.payments,
                    otherPayment: mockData.otherPayments,
                    supplier: mockData.suppliers,
                    employee: mockData.employees,
                    expense: mockData.expenses,
                    sale: mockData.sales
                };

                const array = dataArrays[type];
                if (!array) return;

                const itemIndex = array.findIndex(item => item.id == id);
                if (itemIndex !== -1) {
                    array.splice(itemIndex, 1);

                    // حفظ البيانات في التخزين المحلي فوراً
                    saveDataToLocalStorage();

                    showNotification('تم حذف العنصر بنجاح! 🗑️', 'success');

                    // إعادة تحميل القسم لإظهار التحديثات
                    forceReloadSection(getCurrentSection());
                } else {
                    showNotification('لم يتم العثور على العنصر! ❌', 'error');
                }
            }
        }

        function getFieldLabel(key) {
            const labels = {
                id: 'الرقم',
                name: 'الاسم',
                supplier: 'المورد',
                invoice: 'رقم الفاتورة',
                amount: 'المبلغ',
                date: 'التاريخ',
                status: 'الحالة',
                items: 'عدد الأصناف',
                contact: 'الشخص المسؤول',
                phone: 'الهاتف',
                email: 'البريد الإلكتروني',
                city: 'المدينة',
                position: 'المنصب',
                department: 'القسم',
                salary: 'الراتب',
                description: 'الوصف',
                category: 'الفئة',
                method: 'طريقة الدفع',
                customer: 'العميل',
                total: 'الإجمالي',
                source: 'المصدر',
                totalPurchases: 'إجمالي المشتريات',
                balance: 'الرصيد المستحق',
                previousBalance: 'المبلغ السابق',
                totalOutstanding: 'إجمالي المستحقات',
                purchaseCount: 'عدد المشتريات',
                lastPurchaseDate: 'آخر تاريخ شراء'
            };
            return labels[key] || key;
        }

        function getTypeLabel(type) {
            const labels = {
                purchase: 'المشتريات',
                payment: 'المدفوعات',
                supplier: 'المورد',
                employee: 'الموظف',
                expense: 'المصروف',
                sale: 'المبيعة'
            };
            return labels[type] || type;
        }

        function confirmPayment(id) {
            if (confirm('هل أنت متأكد من تأكيد هذه الدفعة؟')) {
                // تحديث حالة الدفعة في البيانات الوهمية
                const payment = mockData.payments.find(p => p.id == id);
                if (payment) {
                    payment.status = 'مدفوع';
                    showNotification('تم تأكيد دفعة المورد بنجاح! ✅', 'success');
                    // إعادة تحميل الصفحة لإظهار التحديث
                    forceReloadSection('payments');
                }
            }
        }

        function confirmOtherPayment(id) {
            if (confirm('هل أنت متأكد من تأكيد هذه الدفعة؟')) {
                // تحديث حالة الدفعة في البيانات الوهمية
                const payment = mockData.otherPayments.find(p => p.id == id);
                if (payment) {
                    payment.status = 'مدفوع';
                    showNotification('تم تأكيد الدفعة الأخرى بنجاح! ✅', 'success');
                    // إعادة تحميل الصفحة لإظهار التحديث
                    forceReloadSection('payments');
                }
            }
        }

        function updateItemData(type, id, newData) {
            const dataArrays = {
                purchase: mockData.purchases,
                payment: mockData.payments,
                otherPayment: mockData.otherPayments,
                supplier: mockData.suppliers,
                employee: mockData.employees,
                expense: mockData.expenses,
                sale: mockData.sales
            };

            const array = dataArrays[type];
            if (!array) return false;

            const itemIndex = array.findIndex(item => item.id == id);
            if (itemIndex === -1) return false;

            // تحديث البيانات (تحويل الأرقام من النص إلى رقم)
            Object.keys(newData).forEach(key => {
                if (key !== 'id' && key !== 'type') {
                    let value = newData[key];

                    // تحويل الحقول الرقمية
                    if (['amount', 'items', 'salary', 'total', 'rating'].includes(key)) {
                        value = parseFloat(value) || 0;
                    }

                    array[itemIndex][key] = value;
                }
            });

            // حفظ البيانات في التخزين المحلي فوراً
            saveDataToLocalStorage();

            return true;
        }

        function validateFormData(data, type) {
            console.log('التحقق من البيانات:', data, 'النوع:', type);

            const requiredFields = {
                purchase: ['supplier', 'amount'], // إزالة invoice من الحقول المطلوبة لأنه يُولد تلقائياً
                payment: ['recipient', 'amount', 'method', 'description'],
                otherPayment: ['recipient', 'amount', 'method', 'category', 'description'],
                supplier: ['name', 'contact', 'phone', 'email'],
                employee: ['name', 'position', 'department', 'salary'],
                expense: ['description', 'amount', 'category'],
                sale: ['customer', 'total', 'method']
            };

            const required = requiredFields[type] || [];
            console.log('الحقول المطلوبة:', required);

            for (let field of required) {
                const value = data[field];
                console.log(`فحص الحقل ${field}:`, value);

                if (!value || (typeof value === 'string' && value.trim() === '')) {
                    console.log(`حقل مطلوب فارغ: ${field}`);
                    showNotification(`الحقل "${getFieldLabel(field)}" مطلوب! ❌`, 'error');
                    return false;
                }
            }

            // التحقق من الحقول الرقمية
            const numericFields = ['amount', 'salary', 'total'];
            for (let field of numericFields) {
                if (data[field] && isNaN(parseFloat(data[field]))) {
                    console.log(`حقل رقمي غير صحيح: ${field}`);
                    showNotification(`الحقل "${getFieldLabel(field)}" يجب أن يكون رقماً! ❌`, 'error');
                    return false;
                }
            }

            console.log('تم التحقق من البيانات بنجاح');
            return true;
        }

        function addNewItemToData(type, data) {
            console.log('بدء إضافة عنصر جديد:', type, data);

            const dataArrays = {
                purchase: mockData.purchases,
                payment: mockData.payments,
                otherPayment: mockData.otherPayments,
                supplier: mockData.suppliers,
                employee: mockData.employees,
                expense: mockData.expenses,
                sale: mockData.sales,
                product: mockData.products,
                dailyReport: mockData.dailyReports
            };

            const array = dataArrays[type];
            if (!array) {
                console.error('نوع البيانات غير صحيح:', type);
                return false;
            }

            console.log('المصفوفة قبل الإضافة:', array.length, 'عنصر');

            // إنشاء ID جديد
            const newId = array.length > 0 ? Math.max(...array.map(item => item.id)) + 1 : 1;
            console.log('ID الجديد:', newId);

            // إنشاء العنصر الجديد حسب النوع
            let newItem = { id: newId };

            // تحويل البيانات وإضافة الحقول المطلوبة
            Object.keys(data).forEach(key => {
                let value = data[key];

                // تحويل الحقول الرقمية
                if (['amount', 'items', 'salary', 'total', 'rating'].includes(key)) {
                    value = parseFloat(value) || 0;
                }

                newItem[key] = value;
            });

            // إضافة حقول افتراضية حسب النوع
            const currentDate = new Date().toISOString().split('T')[0];

            switch(type) {
                case 'purchase':
                    newItem.date = currentDate;
                    newItem.status = newItem.status || 'في الانتظار';
                    if (!newItem.items) newItem.items = 1;

                    // معالجة مبلغ المشترى بشكل صحيح
                    const purchaseAmountValidation = validateAmount(newItem.amount, 'مبلغ المشترى');
                    if (purchaseAmountValidation.valid) {
                        newItem.amount = purchaseAmountValidation.value;
                    } else {
                        console.error(`❌ ${purchaseAmountValidation.message}`);
                        return false; // إيقاف العملية إذا كان المبلغ غير صحيح
                    }

                    // ربط المشتريات بالمورد
                    const supplier = mockData.suppliers.find(s => s.name === newItem.supplier);
                    if (supplier) {
                        newItem.supplier_id = supplier.id;

                        // استخدام رقم الفاتورة المولد تلقائياً أو توليد واحد جديد
                        if (!newItem.invoice || newItem.invoice === '') {
                            newItem.invoice = generateNextInvoiceNumber(supplier.id);
                        }

                        // إضافة رقم الفاتورة إلى البيانات
                        newItem.invoice_number = newItem.invoice;

                        // تحديث آخر رقم فاتورة للمورد
                        updateSupplierLastInvoiceNumber(supplier.id, newItem.invoice);

                        // تحديث رصيد المورد بعد إضافة المشترى
                        updateSupplierBalance(supplier.id);

                        console.log(`📋 تم ربط المشترى بالمورد ${supplier.name} برقم فاتورة ${newItem.invoice}`);
                        console.log(`💰 مبلغ المشترى: ${formatNumber(newItem.amount)} ر.س`);
                        console.log(`💰 تم تحديث رصيد المورد: ${formatNumber(supplier.balance)} ر.س`);
                    } else {
                        console.error('❌ لم يتم العثور على المورد:', newItem.supplier);
                        // استخدام رقم فاتورة افتراضي
                        newItem.invoice = `INV-2024-${String(newId).padStart(3, '0')}`;
                        newItem.invoice_number = newItem.invoice;
                    }

                    console.log('تم إنشاء طلب شراء جديد مع ربط المورد:', newItem);
                    break;
                case 'payment':
                    newItem.date = currentDate;
                    newItem.dueDate = currentDate;
                    newItem.status = newItem.status || 'معلق';
                    newItem.category = 'موردين';
                    newItem.type = 'supplier';
                    if (!newItem.description) newItem.description = 'دفعة للموردين';

                    // معالجة مبلغ الدفعة بشكل صحيح
                    const paymentAmountValidation = validateAmount(newItem.amount, 'مبلغ الدفعة');
                    if (paymentAmountValidation.valid) {
                        newItem.amount = paymentAmountValidation.value;
                    } else {
                        console.error(`❌ ${paymentAmountValidation.message}`);
                        return false; // إيقاف العملية إذا كان المبلغ غير صحيح
                    }

                    // ربط المدفوعات بالمورد
                    const paymentSupplier = mockData.suppliers.find(s => s.name === newItem.recipient);
                    if (paymentSupplier) {
                        newItem.supplier_id = paymentSupplier.id;

                        // تحديث رصيد المورد بعد إضافة الدفعة
                        updateSupplierBalance(paymentSupplier.id);

                        console.log(`💳 تم ربط الدفعة بالمورد ${paymentSupplier.name}`);
                        console.log(`💰 مبلغ الدفعة: ${formatNumber(newItem.amount)} ر.س`);
                        console.log(`💰 تم تحديث رصيد المورد: ${formatNumber(paymentSupplier.balance)} ر.س`);
                    }

                    console.log('تم إنشاء دفعة مورد جديدة مع ربط المورد:', newItem);
                    break;
                case 'otherPayment':
                    newItem.date = currentDate;
                    newItem.dueDate = currentDate;
                    newItem.status = newItem.status || 'معلق';
                    newItem.type = 'other';
                    if (!newItem.description) newItem.description = 'دفعة أخرى';

                    // معالجة مبلغ الدفعة الأخرى بشكل صحيح
                    const otherPaymentAmountValidation = validateAmount(newItem.amount, 'مبلغ الدفعة');
                    if (otherPaymentAmountValidation.valid) {
                        newItem.amount = otherPaymentAmountValidation.value;
                        console.log(`💰 مبلغ الدفعة الأخرى: ${formatNumber(newItem.amount)} ر.س`);
                    } else {
                        console.error(`❌ ${otherPaymentAmountValidation.message}`);
                        return false; // إيقاف العملية إذا كان المبلغ غير صحيح
                    }

                    console.log('تم إنشاء دفعة أخرى جديدة:', newItem);
                    break;
                case 'supplier':
                    newItem.totalPurchases = 0;
                    newItem.balance = 0;

                    // معالجة الرصيد السابق بشكل صحيح
                    const previousBalanceValidation = validateAmount(newItem.previousBalance, 'الرصيد السابق');
                    if (previousBalanceValidation.valid) {
                        newItem.previousBalance = previousBalanceValidation.value;
                    } else {
                        newItem.previousBalance = 0;
                        console.warn(`⚠️ ${previousBalanceValidation.message}, تم تعيين الرصيد السابق إلى 0`);
                    }

                    newItem.city = newItem.city || 'غير محدد';
                    newItem.lastInvoiceNumber = '000';

                    // إنشاء رمز فاتورة مخصص للمورد الجديد
                    newItem.invoicePrefix = generateSupplierPrefix(newItem.name);

                    console.log('تم إنشاء مورد جديد:', newItem);
                    console.log(`💰 الرصيد السابق: ${formatNumber(newItem.previousBalance)} ر.س`);
                    console.log(`🏷️ رمز الفواتير للمورد الجديد: ${newItem.invoicePrefix}`);
                    break;
                case 'employee':
                    newItem.employeeId = `EMP-${String(newId).padStart(3, '0')}`;
                    newItem.status = newItem.status || 'نشط';
                    newItem.phone = newItem.phone || 'غير محدد';
                    newItem.email = newItem.email || 'غير محدد';
                    newItem.salary = parseFloat(newItem.salary) || 0;
                    console.log('تم إنشاء موظف جديد:', newItem);
                    break;
                case 'expense':
                    newItem.date = currentDate;
                    newItem.status = newItem.status || 'في الانتظار';
                    newItem.addedBy = 'المستخدم الحالي';
                    if (!newItem.method) newItem.method = 'نقدي';
                    break;
                case 'sale':
                    newItem.invoice = `INV-2024-${String(newId).padStart(3, '0')}`;
                    newItem.date = currentDate;
                    newItem.status = newItem.status || 'مكتمل';
                    newItem.salesperson = 'المستخدم الحالي';
                    if (!newItem.source) newItem.source = 'في المتجر';
                    break;
                case 'product':
                    newItem.id = `P${String(newId).padStart(3, '0')}`;
                    newItem.sku = `${newItem.category.substring(0,2).toUpperCase()}-${String(newId).padStart(3, '0')}`;
                    newItem.supplier_id = 1; // افتراضي
                    console.log('تم إنشاء منتج جديد:', newItem);
                    break;
                case 'dailyReport':
                    // معالجة خاصة للتقارير اليومية
                    newItem = processDailyReportData(data);
                    console.log('تم إنشاء تقرير يومي جديد:', newItem);
                    break;
            }

            console.log('العنصر الجديد:', newItem);

            // إضافة العنصر للمصفوفة
            array.push(newItem);

            // حفظ البيانات في التخزين المحلي فوراً
            saveDataToLocalStorage();

            console.log('المصفوفة بعد الإضافة:', array.length, 'عنصر');
            console.log('آخر عنصر مضاف:', array[array.length - 1]);
            console.log('💾 تم حفظ البيانات تلقائياً في التخزين المحلي');

            return true;
        }

        function getCurrentSection() {
            console.log('القسم الحالي المحفوظ:', currentActiveSection);
            return currentActiveSection;
        }

        function forceReloadSection(section) {
            console.log('إعادة تحميل بقوة للقسم:', section);

            // مسح المحتوى الحالي
            const content = document.getElementById('main-content');
            content.innerHTML = '<div class="flex items-center justify-center h-64"><p class="text-lg">جاري التحميل...</p></div>';

            // تأخير قصير ثم إعادة التحميل
            setTimeout(() => {
                showSection(section);
                console.log('تم إعادة تحميل القسم بنجاح');
            }, 200);
        }

        function searchItems(searchTerm, section) {
            if (!searchTerm.trim()) {
                showSection(section);
                return;
            }

            const searchFields = {
                purchases: ['supplier', 'invoice', 'description'],
                payments: ['recipient', 'category', 'description'],
                suppliers: ['name', 'contact', 'email', 'city'],
                employees: ['name', 'position', 'department', 'email'],
                expenses: ['description', 'category', 'addedBy'],
                sales: ['customer', 'invoice', 'phone']
            };

            const dataArrays = {
                purchases: mockData.purchases,
                payments: mockData.payments,
                suppliers: mockData.suppliers,
                employees: mockData.employees,
                expenses: mockData.expenses,
                sales: mockData.sales
            };

            const fields = searchFields[section];
            const data = dataArrays[section];

            if (!fields || !data) {
                showNotification('قسم غير صحيح للبحث! ❌', 'error');
                return;
            }

            const filteredData = data.filter(item => {
                return fields.some(field => {
                    const value = item[field];
                    return value && value.toString().toLowerCase().includes(searchTerm.toLowerCase());
                });
            });

            if (filteredData.length === 0) {
                showNotification(`لم يتم العثور على نتائج للبحث: "${searchTerm}" 🔍`, 'warning');
            } else {
                showNotification(`تم العثور على ${filteredData.length} نتيجة للبحث 🔍`, 'success');
            }

            // تحديث البيانات مؤقتاً لإظهار النتائج المفلترة
            const originalData = { ...mockData };
            mockData[section] = filteredData;
            showSection(section);

            // إعادة البيانات الأصلية بعد 10 ثوانٍ
            setTimeout(() => {
                Object.assign(mockData, originalData);
            }, 10000);
        }

        // إضافة مستمعات للبحث في جميع حقول البحث
        function addSearchListeners() {
            document.addEventListener('input', function(e) {
                if (e.target.placeholder && e.target.placeholder.includes('البحث')) {
                    const searchTerm = e.target.value;
                    const currentSection = getCurrentSection();

                    // تأخير البحث لتحسين الأداء
                    clearTimeout(window.searchTimeout);
                    window.searchTimeout = setTimeout(() => {
                        if (searchTerm.length >= 2 || searchTerm.length === 0) {
                            searchItems(searchTerm, currentSection);
                        }
                    }, 500);
                }
            });
        }

        // تحديث التاريخ والوقت
        function updateDateTime() {
            const now = new Date();

            // تحديث التاريخ الميلادي
            const dateOptions = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            const currentDate = now.toLocaleDateString('ar-SA', dateOptions);

            // تحديث التاريخ الهجري
            let hijriDate;
            try {
                hijriDate = now.toLocaleDateString('ar-SA-u-ca-islamic', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            } catch (e) {
                // في حالة عدم دعم التقويم الهجري، استخدم تاريخ تقريبي
                hijriDate = 'غير متوفر';
            }

            // تحديث الوقت
            const timeOptions = {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            };
            const currentTime = now.toLocaleTimeString('ar-SA', timeOptions);

            // تحديث العناصر في الصفحة
            const dateElement = document.getElementById('current-date');
            const hijriElement = document.getElementById('hijri-date');
            const timeElement = document.getElementById('last-update');

            if (dateElement) {
                dateElement.textContent = `اليوم: ${currentDate}`;
            }

            if (hijriElement) {
                hijriElement.textContent = `التاريخ الهجري: ${hijriDate}`;
            }

            if (timeElement) {
                timeElement.textContent = `آخر تحديث: ${currentTime}`;
            }

            // تحديث الساعة في لوحة التحكم إذا كانت موجودة
            const dashboardClock = document.getElementById('dashboard-clock');
            const dashboardHijri = document.getElementById('dashboard-hijri');

            if (dashboardClock) {
                dashboardClock.textContent = currentTime;
            }

            if (dashboardHijri) {
                dashboardHijri.textContent = hijriDate;
            }
        }

        // تحديث التاريخ والوقت كل ثانية
        function startDateTimeUpdater() {
            updateDateTime(); // تحديث فوري
            setInterval(updateDateTime, 1000); // تحديث كل ثانية
        }

        // دالة اختبار لإضافة بيانات تجريبية
        function testAddData() {
            console.log('اختبار إضافة البيانات...');

            try {
                // اختبار إضافة مورد
                const testSupplier = {
                    name: 'شركة اختبار',
                    contact: 'أحمد محمد',
                    phone: '+966501234567',
                    email: '<EMAIL>',
                    city: 'الرياض'
                };

                console.log('إضافة مورد تجريبي:', testSupplier);
                const supplierSuccess = addNewItemToData('supplier', testSupplier);
                console.log('نتيجة إضافة المورد:', supplierSuccess);

                // اختبار إضافة موظف
                const testEmployee = {
                    name: 'موظف تجريبي',
                    position: 'مطور',
                    department: 'التقنية',
                    phone: '+966509876543',
                    email: '<EMAIL>',
                    salary: '8000'
                };

                console.log('إضافة موظف تجريبي:', testEmployee);
                const employeeSuccess = addNewItemToData('employee', testEmployee);
                console.log('نتيجة إضافة الموظف:', employeeSuccess);

                if (supplierSuccess && employeeSuccess) {
                    console.log('تم اختبار الإضافة بنجاح!');
                    showNotification('تم إضافة البيانات التجريبية بنجاح! 🧪', 'success');
                    forceReloadSection(getCurrentSection());
                } else {
                    showNotification('فشل في إضافة بعض البيانات التجريبية! ❌', 'error');
                }
            } catch (error) {
                console.error('خطأ في اختبار البيانات:', error);
                showNotification(`خطأ في الاختبار: ${error.message} ❌`, 'error');
            }
        }

        // دالة تشخيص سريعة
        function diagnoseSystem() {
            console.log('=== تشخيص النظام ===');
            console.log('البيانات الوهمية:', mockData);
            console.log('عدد الموردين:', mockData.suppliers ? mockData.suppliers.length : 'غير موجود');
            console.log('عدد الموظفين:', mockData.employees ? mockData.employees.length : 'غير موجود');
            console.log('عدد المدفوعات:', mockData.payments ? mockData.payments.length : 'غير موجود');
            console.log('عدد المدفوعات الأخرى:', mockData.otherPayments ? mockData.otherPayments.length : 'غير موجود');
            console.log('القسم الحالي:', currentActiveSection);
            console.log('===================');

            showNotification('تم عرض تشخيص النظام في وحدة التحكم (F12) 🔍', 'info');
        }

        // دالة اختبار مبسطة للحفظ المباشر
        function testDirectSave() {
            console.log('🧪 اختبار الحفظ المباشر...');

            try {
                // اختبار حفظ مورد بسيط
                const testData = {
                    name: 'مورد تجريبي',
                    contact: 'شخص تجريبي',
                    phone: '123456789',
                    email: '<EMAIL>'
                };

                console.log('📤 اختبار حفظ مورد:', testData);
                const result = addNewItemToData('supplier', testData);
                console.log('📊 نتيجة الاختبار:', result);

                if (result) {
                    showNotification('✅ اختبار الحفظ المباشر نجح!', 'success');
                    forceReloadSection(getCurrentSection());
                } else {
                    showNotification('❌ اختبار الحفظ المباشر فشل!', 'error');
                }

            } catch (error) {
                console.error('💥 خطأ في اختبار الحفظ المباشر:', error);
                showNotification(`خطأ في الاختبار: ${error.message}`, 'error');
            }
        }

        // دالة اختبار النموذج
        function testFormModal() {
            console.log('🧪 اختبار فتح نموذج...');
            try {
                addNewItem('supplier');
                console.log('✅ تم فتح النموذج بنجاح');
            } catch (error) {
                console.error('💥 خطأ في فتح النموذج:', error);
                showNotification(`خطأ في فتح النموذج: ${error.message}`, 'error');
            }
        }

        // دالة اختبار عرض تفاصيل المورد
        function testViewSupplier() {
            console.log('🧪 اختبار عرض تفاصيل المورد...');
            try {
                viewItem('supplier', 1);
                console.log('✅ تم عرض تفاصيل المورد بنجاح');
            } catch (error) {
                console.error('💥 خطأ في عرض تفاصيل المورد:', error);
                showNotification(`خطأ في عرض التفاصيل: ${error.message}`, 'error');
            }
        }

        // دالة اختبار الحفظ والاسترداد
        function testSaveLoad() {
            console.log('🧪 اختبار الحفظ والاسترداد...');
            try {
                // حفظ البيانات الحالية
                const saveResult = saveDataToLocalStorage();
                if (saveResult) {
                    console.log('✅ تم الحفظ بنجاح');

                    // محاولة الاسترداد
                    const loadResult = loadDataFromLocalStorage();
                    if (loadResult) {
                        console.log('✅ تم الاسترداد بنجاح');
                        showNotification('اختبار الحفظ والاسترداد نجح! 💾', 'success');
                    } else {
                        console.log('❌ فشل في الاسترداد');
                        showNotification('فشل في اختبار الاسترداد! ❌', 'error');
                    }
                } else {
                    console.log('❌ فشل في الحفظ');
                    showNotification('فشل في اختبار الحفظ! ❌', 'error');
                }
            } catch (error) {
                console.error('💥 خطأ في اختبار الحفظ والاسترداد:', error);
                showNotification(`خطأ في الاختبار: ${error.message}`, 'error');
            }
        }

        // دالة اختبار الأقسام
        function testSections() {
            console.log('🧪 اختبار الأقسام...');
            const sections = ['dashboard', 'purchases', 'payments', 'suppliers', 'employees', 'expenses', 'sales', 'reports', 'settings'];

            sections.forEach((section, index) => {
                setTimeout(() => {
                    console.log(`🔄 اختبار قسم: ${section}`);
                    try {
                        showSection(section);
                        console.log(`✅ تم عرض قسم ${section} بنجاح`);
                    } catch (error) {
                        console.error(`❌ خطأ في عرض قسم ${section}:`, error);
                    }
                }, index * 1000);
            });

            showNotification('بدء اختبار جميع الأقسام... 🔄', 'info');
        }

        // دالة اختبار نظام الفواتير المخصص
        function testInvoiceSystem() {
            console.log('🧪 اختبار نظام الفواتير المخصص...');

            try {
                console.log('\n🏷️ رموز الموردين الحالية:');
                mockData.suppliers.forEach(supplier => {
                    console.log(`🏢 ${supplier.name}: ${supplier.invoicePrefix || 'غير محدد'}`);
                });

                console.log('\n📋 اختبار توليد أرقام الفواتير:');
                // اختبار توليد أرقام الفواتير لكل مورد
                mockData.suppliers.forEach(supplier => {
                    console.log(`\n📋 اختبار المورد: ${supplier.name}`);
                    console.log(`🏷️ رمز الفواتير: ${supplier.invoicePrefix}`);
                    console.log(`📊 آخر رقم فاتورة: ${supplier.lastInvoiceNumber}`);

                    const nextInvoice = generateNextInvoiceNumber(supplier.id);
                    console.log(`🔢 الرقم التالي المولد: ${nextInvoice}`);

                    // التحقق من صحة الرقم
                    const isValid = validateInvoiceNumber(supplier.id, nextInvoice);
                    console.log(`✅ صحة الرقم: ${isValid ? 'صحيح' : 'خطأ'}`);
                });

                console.log('\n🎯 نتائج الاختبار:');
                console.log('✅ تم توليد أرقام فواتير مخصصة لكل مورد');
                console.log('✅ كل مورد له رمز فريد يمنع التداخل');
                console.log('✅ التحقق من عدم التكرار يعمل بشكل صحيح');
                console.log('✅ نظام الفواتير المخصص يعمل بشكل مثالي');

                showNotification('اختبار نظام الفواتير المخصص نجح! 🎉', 'success');

            } catch (error) {
                console.error('❌ خطأ في اختبار نظام الفواتير:', error);
                showNotification('فشل في اختبار نظام الفواتير! ❌', 'error');
            }
        }

        // دالة اختبار إنشاء رموز للموردين الجدد
        function testSupplierPrefixGeneration() {
            console.log('🧪 اختبار إنشاء رموز إنجليزية للموردين الجدد...');

            const testNames = [
                'شركة الأمل للتجارة',
                'مؤسسة النور للمواد الغذائية',
                'شركة الفجر للإلكترونيات',
                'مجموعة الخليج التجارية',
                'شركة المستقبل للتقنية المتقدمة',
                'مؤسسة البناء والتطوير',
                'شركة النقل والخدمات اللوجستية',
                'مكتب الاستشارات الهندسية',
                'دار الطباعة والنشر',
                'شركة الصناعات الغذائية',
                'مؤسسة الخدمات الطبية',
                'شركة التسويق الرقمي',
                'مجموعة الاستثمار العقاري',
                'شركة الطاقة المتجددة',
                'مؤسسة التدريب والتطوير'
            ];

            try {
                console.log('\n🏷️ اختبار إنشاء رموز إنجليزية:');
                testNames.forEach(name => {
                    const prefix = generateSupplierPrefix(name);
                    console.log(`🏢 "${name}" → ${prefix}`);
                });

                console.log('\n✅ مزايا الرموز الإنجليزية:');
                console.log('   🌐 لا تتأثر بتغيير اللغة');
                console.log('   💾 متوافقة مع جميع قواعد البيانات');
                console.log('   📊 سهلة الفرز والبحث');
                console.log('   🔤 متوافقة مع الأنظمة الدولية');
                console.log('   📱 تعمل على جميع الأجهزة');

                showNotification('اختبار إنشاء الرموز الإنجليزية نجح! 🏷️', 'success');

            } catch (error) {
                console.error('❌ خطأ في اختبار إنشاء الرموز:', error);
                showNotification('فشل في اختبار إنشاء الرموز! ❌', 'error');
            }
        }

        // دالة اختبار إضافة مشترى مع رقم فاتورة تلقائي
        function testAutoPurchase() {
            console.log('🧪 اختبار إضافة مشترى مع رقم فاتورة تلقائي...');

            try {
                // عرض الرصيد قبل الإضافة
                const supplier = mockData.suppliers.find(s => s.name === 'شركة التوريد المحدودة');
                const balanceBefore = calculateSupplierBalance(supplier.id);
                console.log(`💰 رصيد المورد قبل الإضافة: ${balanceBefore}`);

                // بيانات مشترى تجريبي
                const testPurchase = {
                    supplier: 'شركة التوريد المحدودة',
                    amount: 5000,
                    items: 10,
                    description: 'مشترى تجريبي لاختبار النظام'
                };

                console.log('📦 بيانات المشترى التجريبي:', testPurchase);

                // إضافة المشترى
                const result = addNewItemToData('purchase', testPurchase);

                if (result) {
                    console.log('✅ تم إضافة المشترى بنجاح');

                    // البحث عن المشترى المضاف
                    const addedPurchase = mockData.purchases[mockData.purchases.length - 1];
                    console.log('📋 المشترى المضاف:', addedPurchase);
                    console.log(`🔢 رقم الفاتورة المولد: ${addedPurchase.invoice}`);

                    // عرض الرصيد بعد الإضافة
                    const balanceAfter = calculateSupplierBalance(supplier.id);
                    console.log(`💰 رصيد المورد بعد الإضافة: ${balanceAfter}`);
                    console.log(`📈 الزيادة في الرصيد: ${balanceAfter - balanceBefore}`);

                    showNotification(`تم إضافة مشترى برقم فاتورة ${addedPurchase.invoice} وتحديث الرصيد! 🎉`, 'success');
                } else {
                    console.log('❌ فشل في إضافة المشترى');
                    showNotification('فشل في إضافة المشترى! ❌', 'error');
                }

            } catch (error) {
                console.error('❌ خطأ في اختبار إضافة المشترى:', error);
                showNotification('خطأ في اختبار إضافة المشترى! ❌', 'error');
            }
        }

        // دالة اختبار حساب الأرصدة
        function testBalanceCalculation() {
            console.log('🧪 اختبار حساب أرصدة الموردين...');

            try {
                console.log('\n📊 أرصدة الموردين الحالية:');
                mockData.suppliers.forEach(supplier => {
                    const balance = calculateSupplierBalance(supplier.id);
                    console.log(`🏢 ${supplier.name}:`);
                    console.log(`   💰 الرصيد السابق: ${supplier.previousBalance || 0}`);
                    console.log(`   📦 إجمالي المشتريات: ${mockData.purchases.filter(p => p.supplier_id == supplier.id).reduce((sum, p) => sum + p.amount, 0)}`);
                    console.log(`   💳 إجمالي المدفوعات: ${mockData.payments.filter(p => p.supplier_id == supplier.id).reduce((sum, p) => sum + p.amount, 0)}`);
                    console.log(`   💰 الرصيد الحالي: ${balance}`);
                    console.log(`   📊 المستحقات: ${Math.max(0, balance)}`);
                    console.log('');
                });

                showNotification('تم عرض أرصدة جميع الموردين في وحدة التحكم! 📊', 'info');

            } catch (error) {
                console.error('❌ خطأ في اختبار حساب الأرصدة:', error);
                showNotification('خطأ في اختبار حساب الأرصدة! ❌', 'error');
            }
        }

        // دالة اختبار واجهة الموردين الجديدة
        function testSuppliersInterface() {
            console.log('🧪 اختبار واجهة الموردين الجديدة...');

            try {
                // تحديث بيانات الموردين
                updateSuppliersData();

                // عرض قسم الموردين
                showSection('suppliers');

                console.log('✅ تم عرض واجهة الموردين المحدثة');
                console.log('📋 الواجهة تعرض الآن:');
                console.log('   📦 إجمالي المشتريات');
                console.log('   💳 إجمالي المدفوعات');
                console.log('   💰 إجمالي المستحقات');
                console.log('❌ تم إخفاء:');
                console.log('   ⭐ التقييم');
                console.log('   🔢 عدد المشتريات (نقل للعرض التفصيلي)');
                console.log('   💰 الرصيد السابق (نقل للعرض التفصيلي)');

                showNotification('تم تحديث واجهة الموردين بنجاح! 🎨', 'success');

            } catch (error) {
                console.error('❌ خطأ في اختبار واجهة الموردين:', error);
                showNotification('خطأ في اختبار واجهة الموردين! ❌', 'error');
            }
        }

        // دالة اختبار معالجة الأرقام
        function testNumberProcessing() {
            console.log('🧪 اختبار معالجة الأرقام...');

            const testValues = [
                '15000',
                '15,000',
                '15 000',
                '15000.50',
                '15,000.75',
                '15000 ر.س',
                '15,000 ريال',
                'abc15000',
                '15000abc',
                '',
                null,
                undefined,
                '0',
                '-5000',
                '999999999',
                '1000000000'
            ];

            try {
                console.log('\n🔢 اختبار تحويل الأرقام:');
                testValues.forEach(value => {
                    const result = parseNumber(value);
                    console.log(`"${value}" → ${result}`);
                });

                console.log('\n✅ اختبار التحقق من صحة المبالغ:');
                const testAmounts = ['15000', '-5000', '999999999', '1000000000', 'abc'];
                testAmounts.forEach(amount => {
                    const validation = validateAmount(amount, 'مبلغ تجريبي');
                    console.log(`"${amount}" → صحيح: ${validation.valid}, القيمة: ${validation.value}, الرسالة: ${validation.message}`);
                });

                console.log('\n📊 اختبار تنسيق الأرقام:');
                const formatTests = [15000, 15000.5, 1234567.89];
                formatTests.forEach(num => {
                    console.log(`${num} → ${formatNumber(num)} (بدون كسور)`);
                    console.log(`${num} → ${formatNumber(num, 2)} (بكسرين)`);
                });

                showNotification('اختبار معالجة الأرقام نجح! 🔢', 'success');

            } catch (error) {
                console.error('❌ خطأ في اختبار معالجة الأرقام:', error);
                showNotification('فشل في اختبار معالجة الأرقام! ❌', 'error');
            }
        }

        // دالة اختبار تحسين جدول المشتريات
        function testPurchasesTableImprovement() {
            console.log('🧪 اختبار تحسين جدول المشتريات...');

            try {
                // عرض قسم المشتريات
                showSection('purchases');

                console.log('✅ تحسينات جدول المشتريات:');
                console.log('   🚫 تم حذف السهم الأفقي');
                console.log('   📐 تم تطبيق table-layout: fixed');
                console.log('   🎯 تم توسيط جميع النصوص');
                console.log('   ❌ تم حذف عمود المورد');
                console.log('   ❌ تم حذف عمود عدد الأصناف');
                console.log('   📊 تم تحسين توزيع العرض:');
                console.log('      - رقم الفاتورة: 20%');
                console.log('      - التاريخ: 25%');
                console.log('      - المبلغ: 25%');
                console.log('      - الحالة: 15%');
                console.log('      - الإجراءات: 15%');

                console.log('\n🎨 تحسينات التنسيق:');
                console.log('   📱 responsive design');
                console.log('   🎯 text-align: center لجميع الخلايا');
                console.log('   📏 عرض ثابت للأعمدة');
                console.log('   💡 tooltips للنصوص الطويلة');
                console.log('   🎨 تحسين شارات الحالة');

                showNotification('تم تحسين جدول المشتريات بنجاح! 📊', 'success');

            } catch (error) {
                console.error('❌ خطأ في اختبار تحسين الجدول:', error);
                showNotification('فشل في اختبار تحسين الجدول! ❌', 'error');
            }
        }

        // دالة اختبار تأثيرات التمرير المخصصة
        function testHoverEffects() {
            console.log('🧪 اختبار تأثيرات التمرير المخصصة...');

            try {
                console.log('✅ تأثيرات التمرير المطبقة:');
                console.log('\n📊 الجداول:');
                console.log('   🎯 تأثير scale(1.02) عند التمرير');
                console.log('   💫 box-shadow محسن');
                console.log('   🎨 border-radius ديناميكي');
                console.log('   ⚡ transition سلس (0.3s)');
                console.log('   🌙 دعم الوضع المظلم');

                console.log('\n🃏 البطاقات العامة:');
                console.log('   📈 translateY(-4px) + scale(1.02)');
                console.log('   ✨ box-shadow متدرج');
                console.log('   🔵 border-color تفاعلي');
                console.log('   🎭 تأثيرات مختلفة للوضع المظلم');

                console.log('\n🏢 بطاقات الموردين (مخصصة):');
                console.log('   🔵 فقط تغيير لون الإطار للأزرق');
                console.log('   🚫 بدون تأثير scale أو translateY');
                console.log('   🚫 بدون تأثير box-shadow');
                console.log('   ⚡ transition سلس للون الإطار فقط');
                console.log('   🌙 لون أزرق فاتح في الوضع المظلم');

                console.log('\n🎯 الأقسام المحدثة:');
                console.log('   📦 جدول المشتريات - تأثير كامل');
                console.log('   💳 جدول مدفوعات الموردين - تأثير كامل');
                console.log('   💼 جدول المدفوعات الأخرى - تأثير كامل');
                console.log('   🏢 بطاقات الموردين - تأثير مخصص (إطار أزرق فقط)');
                console.log('   📊 البطاقات الإحصائية - تأثير كامل');

                console.log('\n🎨 المزايا:');
                console.log('   🎯 تخصيص مناسب لكل قسم');
                console.log('   👀 تفاعل بصري متوازن');
                console.log('   📱 متوافق مع جميع الأجهزة');
                console.log('   🌈 انتقالات سلسة ومناسبة');
                console.log('   ⚡ أداء محسن');

                // اختبار عملي
                console.log('\n🧪 اختبار عملي:');
                console.log('1. اذهب لقسم المشتريات ومرر فوق الصفوف - تأثير كامل');
                console.log('2. اذهب لقسم المدفوعات ومرر فوق الجداول - تأثير كامل');
                console.log('3. اذهب لقسم الموردين ومرر فوق البطاقات - إطار أزرق فقط');
                console.log('4. لاحظ الفرق في التأثيرات حسب القسم');

                // عرض قسم الموردين للاختبار
                showSection('suppliers');

                showNotification('تأثيرات التمرير المخصصة تعمل بنجاح! 🎨', 'success');

            } catch (error) {
                console.error('❌ خطأ في اختبار تأثيرات التمرير:', error);
                showNotification('فشل في اختبار تأثيرات التمرير! ❌', 'error');
            }
        }

        // دالة اختبار إصلاح مضاعفة المبالغ
        function testAmountMultiplicationFix() {
            console.log('🧪 اختبار إصلاح مضاعفة المبالغ...');

            try {
                console.log('🔍 فحص المشكلة الأصلية:');
                console.log('❌ المشكلة: استدعاء calculateSupplierBalance() مرتين');
                console.log('❌ النتيجة: مضاعفة المبالغ عشرات المرات');

                console.log('\n✅ الحل المطبق:');
                console.log('🔧 تحديث updateSuppliersData()');
                console.log('🔢 استخدام parseNumber() لمعالجة الأرقام');
                console.log('📊 حساب مباشر بدون استدعاء دوال إضافية');
                console.log('🛡️ منع المضاعفة نهائياً');

                console.log('\n🧮 اختبار الحساب:');
                mockData.suppliers.forEach(supplier => {
                    const previousBalance = parseNumber(supplier.previousBalance) || 0;
                    const totalPurchases = mockData.purchases
                        .filter(p => p.supplier_id === supplier.id)
                        .reduce((sum, p) => sum + parseNumber(p.amount), 0);
                    const totalPayments = mockData.payments
                        .filter(p => p.supplier_id === supplier.id)
                        .reduce((sum, p) => sum + parseNumber(p.amount), 0);
                    const currentBalance = previousBalance + totalPurchases - totalPayments;

                    console.log(`🏢 ${supplier.name}:`);
                    console.log(`   💰 ${formatNumber(previousBalance)} + ${formatNumber(totalPurchases)} - ${formatNumber(totalPayments)} = ${formatNumber(currentBalance)} ر.س`);
                });

                // تحديث البيانات للتأكد
                updateSuppliersData();

                showNotification('تم إصلاح مضاعفة المبالغ بنجاح! 🎯', 'success');

            } catch (error) {
                console.error('❌ خطأ في اختبار إصلاح المضاعفة:', error);
                showNotification('فشل في اختبار إصلاح المضاعفة! ❌', 'error');
            }
        }

        // دوال التقارير التفاعلية
        function addDailyReport() {
            const today = new Date().toISOString().split('T')[0];
            showModal('إدخال المبيعات اليومية', createDailyReportForm(today), 'dailyReport');
        }

        function createDailyReportForm(date) {
            return `
                <form id="itemForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">التاريخ</label>
                        <input type="date" name="date" value="${date}" class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none" required>
                    </div>

                    <div class="border-t pt-4">
                        <h4 class="font-medium mb-3">🏪 المبيعات الداخلية</h4>
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium mb-2">المبيعات النقدية</label>
                                <input type="number" name="cash_sales" class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none" placeholder="0.00" step="0.01">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">مبيعات البطاقات</label>
                                <input type="number" name="card_sales" class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none" placeholder="0.00" step="0.01">
                            </div>
                        </div>
                    </div>

                    <div class="border-t pt-4">
                        <h4 class="font-medium mb-3">🚚 المبيعات الخارجية</h4>
                        <div class="space-y-3">
                            ${['hungerstation', 'talabat', 'jahez', 'mrsool'].map(platform => `
                                <div class="grid grid-cols-3 gap-2">
                                    <div>
                                        <label class="block text-sm font-medium mb-1">${getPlatformName(platform)} - المبلغ</label>
                                        <input type="number" name="${platform}_amount" class="form-input w-full px-2 py-1 border rounded text-sm" placeholder="0.00" step="0.01">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium mb-1">عدد الطلبات</label>
                                        <input type="number" name="${platform}_orders" class="form-input w-full px-2 py-1 border rounded text-sm" placeholder="0">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium mb-1">العمولة</label>
                                        <input type="number" name="${platform}_commission" class="form-input w-full px-2 py-1 border rounded text-sm" placeholder="0.00" step="0.01">
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="flex gap-3 pt-4">
                        <button type="submit" class="btn flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                            💾 حفظ التقرير
                        </button>
                        <button type="button" onclick="closeModal()" class="btn flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400">
                            ❌ إلغاء
                        </button>
                    </div>
                </form>
            `;
        }

        function addNewProduct() {
            showModal('إضافة منتج جديد', createProductForm(), 'product');
        }

        function createProductForm() {
            return `
                <form id="itemForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">اسم المنتج <span class="text-red-500">*</span></label>
                        <input type="text" name="name" class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none" placeholder="أدخل اسم المنتج" required>
                    </div>

                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="block text-sm font-medium mb-2">السعر <span class="text-red-500">*</span></label>
                            <input type="number" name="price" class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none" placeholder="0.00" step="0.01" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">التكلفة <span class="text-red-500">*</span></label>
                            <input type="number" name="cost" class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none" placeholder="0.00" step="0.01" required>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium mb-2">الفئة <span class="text-red-500">*</span></label>
                        <select name="category" class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none" required>
                            <option value="">اختر الفئة</option>
                            <option value="وجبات رئيسية">وجبات رئيسية</option>
                            <option value="بيتزا">بيتزا</option>
                            <option value="سلطات">سلطات</option>
                            <option value="مشروبات">مشروبات</option>
                            <option value="وجبات شعبية">وجبات شعبية</option>
                            <option value="حلويات">حلويات</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium mb-2">الوحدة <span class="text-red-500">*</span></label>
                        <select name="unit" class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none" required>
                            <option value="">اختر الوحدة</option>
                            <option value="قطعة">قطعة</option>
                            <option value="كوب">كوب</option>
                            <option value="كيلو">كيلو</option>
                            <option value="لتر">لتر</option>
                        </select>
                    </div>

                    <div class="flex gap-3 pt-4">
                        <button type="submit" class="btn flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                            💾 حفظ المنتج
                        </button>
                        <button type="button" onclick="closeModal()" class="btn flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400">
                            ❌ إلغاء
                        </button>
                    </div>
                </form>
            `;
        }

        function editProduct(productId) {
            const product = mockData.products.find(p => p.id === productId);
            if (!product) {
                showNotification('لم يتم العثور على المنتج! ❌', 'error');
                return;
            }
            showNotification('سيتم فتح نموذج تعديل المنتج قريباً! 🔧', 'info');
        }

        function deleteProduct(productId) {
            if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
                const index = mockData.products.findIndex(p => p.id === productId);
                if (index !== -1) {
                    mockData.products.splice(index, 1);
                    showNotification('تم حذف المنتج بنجاح! 🗑️', 'success');
                    forceReloadSection('reports');
                }
            }
        }

        function exportReport() {
            showNotification('سيتم إضافة وظيفة التصدير قريباً! 📥', 'info');
        }

        // دالة طباعة تفاصيل المورد
        function printSupplierDetails(supplierId) {
            const supplier = mockData.suppliers.find(s => s.id == supplierId);
            if (!supplier) {
                showNotification('لم يتم العثور على المورد! ❌', 'error');
                return;
            }

            // تحديث بيانات المورد
            updateSuppliersData();
            const updatedSupplier = mockData.suppliers.find(s => s.id == supplierId);
            if (updatedSupplier) {
                Object.assign(supplier, updatedSupplier);
            }

            // الحصول على مشتريات المورد
            const supplierPurchases = mockData.purchases.filter(p => p.supplier_id == supplierId);

            // إنشاء محتوى الطباعة
            const printContent = createPrintableSupplierReport(supplier, supplierPurchases);

            // فتح نافذة طباعة جديدة
            const printWindow = window.open('', '_blank');
            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.focus();
            printWindow.print();

            showNotification('تم فتح نافذة الطباعة! 🖨️', 'success');
        }

        // دالة تصدير المورد إلى PDF
        function exportSupplierToPDF(supplierId) {
            const supplier = mockData.suppliers.find(s => s.id == supplierId);
            if (!supplier) {
                showNotification('لم يتم العثور على المورد! ❌', 'error');
                return;
            }

            // تحديث بيانات المورد
            updateSuppliersData();
            const updatedSupplier = mockData.suppliers.find(s => s.id == supplierId);
            if (updatedSupplier) {
                Object.assign(supplier, updatedSupplier);
            }

            // الحصول على مشتريات المورد
            const supplierPurchases = mockData.purchases.filter(p => p.supplier_id == supplierId);

            // إنشاء محتوى PDF
            const pdfContent = createPDFSupplierReport(supplier, supplierPurchases);

            // محاكاة تصدير PDF (في التطبيق الحقيقي، ستستخدم مكتبة مثل jsPDF)
            const blob = new Blob([pdfContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `تقرير_المورد_${supplier.name}_${new Date().toISOString().split('T')[0]}.html`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showNotification('تم تصدير تقرير المورد! 📄', 'success');
        }

        // دالة إنشاء محتوى قابل للطباعة
        function createPrintableSupplierReport(supplier, purchases) {
            const currentDate = new Date().toLocaleDateString('ar-SA');
            const totalPurchases = purchases.reduce((sum, p) => sum + p.amount, 0);

            return `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>تقرير المورد - ${supplier.name}</title>
                    <style>
                        body {
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            margin: 0;
                            padding: 20px;
                            background: white;
                            color: #333;
                            line-height: 1.6;
                        }
                        .header {
                            text-align: center;
                            border-bottom: 3px solid #2563eb;
                            padding-bottom: 20px;
                            margin-bottom: 30px;
                        }
                        .company-name {
                            font-size: 28px;
                            font-weight: bold;
                            color: #2563eb;
                            margin-bottom: 10px;
                        }
                        .report-title {
                            font-size: 20px;
                            color: #666;
                            margin-bottom: 5px;
                        }
                        .report-date {
                            color: #888;
                            font-size: 14px;
                        }
                        .section {
                            margin-bottom: 30px;
                            background: #f8fafc;
                            padding: 20px;
                            border-radius: 8px;
                            border-right: 4px solid #2563eb;
                        }
                        .section-title {
                            font-size: 18px;
                            font-weight: bold;
                            color: #2563eb;
                            margin-bottom: 15px;
                            display: flex;
                            align-items: center;
                            gap: 10px;
                        }
                        .info-grid {
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                            gap: 15px;
                            margin-bottom: 20px;
                        }
                        .info-item {
                            background: white;
                            padding: 15px;
                            border-radius: 6px;
                            border: 1px solid #e2e8f0;
                        }
                        .info-label {
                            font-weight: bold;
                            color: #4a5568;
                            margin-bottom: 5px;
                        }
                        .info-value {
                            color: #2d3748;
                            font-size: 16px;
                        }
                        .financial-summary {
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            padding: 20px;
                            border-radius: 8px;
                            margin: 20px 0;
                        }
                        .financial-grid {
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                            gap: 15px;
                        }
                        .financial-item {
                            text-align: center;
                            background: rgba(255,255,255,0.1);
                            padding: 15px;
                            border-radius: 6px;
                        }
                        .financial-amount {
                            font-size: 24px;
                            font-weight: bold;
                            margin-bottom: 5px;
                        }
                        .financial-label {
                            font-size: 14px;
                            opacity: 0.9;
                        }
                        .purchases-table {
                            width: 100%;
                            border-collapse: collapse;
                            margin-top: 15px;
                            background: white;
                            border-radius: 6px;
                            overflow: hidden;
                            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                        }
                        .purchases-table th {
                            background: #2563eb;
                            color: white;
                            padding: 12px;
                            text-align: right;
                            font-weight: bold;
                        }
                        .purchases-table td {
                            padding: 12px;
                            border-bottom: 1px solid #e2e8f0;
                            text-align: right;
                        }
                        .purchases-table tr:nth-child(even) {
                            background: #f8fafc;
                        }
                        .total-row {
                            background: #2563eb !important;
                            color: white;
                            font-weight: bold;
                        }
                        .status-badge {
                            padding: 4px 8px;
                            border-radius: 4px;
                            font-size: 12px;
                            font-weight: bold;
                        }
                        .status-received { background: #d1fae5; color: #065f46; }
                        .status-approved { background: #dbeafe; color: #1e40af; }
                        .status-pending { background: #fef3c7; color: #92400e; }
                        .footer {
                            margin-top: 40px;
                            text-align: center;
                            color: #666;
                            font-size: 12px;
                            border-top: 1px solid #e2e8f0;
                            padding-top: 20px;
                        }
                        @media print {
                            body { margin: 0; }
                            .section { break-inside: avoid; }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <div class="company-name">نظام إدارة المحاسبة</div>
                        <div class="report-title">تقرير تفاصيل المورد</div>
                        <div class="report-date">تاريخ التقرير: ${currentDate}</div>
                    </div>

                    <div class="section">
                        <div class="section-title">
                            🏢 المعلومات الأساسية
                        </div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">اسم الشركة</div>
                                <div class="info-value">${supplier.name}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">الشخص المسؤول</div>
                                <div class="info-value">${supplier.contact}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">رقم الهاتف</div>
                                <div class="info-value">${supplier.phone}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">البريد الإلكتروني</div>
                                <div class="info-value">${supplier.email}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">المدينة</div>
                                <div class="info-value">${supplier.city}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">التقييم</div>
                                <div class="info-value">${supplier.rating} ⭐</div>
                            </div>
                        </div>
                    </div>

                    <div class="financial-summary">
                        <div class="section-title" style="color: white; margin-bottom: 20px;">
                            💰 الملخص المالي
                        </div>
                        <div class="financial-grid">
                            <div class="financial-item">
                                <div class="financial-amount">${supplier.totalPurchases.toLocaleString()} ر.س</div>
                                <div class="financial-label">إجمالي المشتريات</div>
                            </div>
                            <div class="financial-item">
                                <div class="financial-amount">${supplier.balance.toLocaleString()} ر.س</div>
                                <div class="financial-label">الرصيد الحالي</div>
                            </div>
                            <div class="financial-item">
                                <div class="financial-amount">${(supplier.previousBalance || 0).toLocaleString()} ر.س</div>
                                <div class="financial-label">المبلغ السابق</div>
                            </div>
                            <div class="financial-item">
                                <div class="financial-amount">${supplier.totalOutstanding.toLocaleString()} ر.س</div>
                                <div class="financial-label">إجمالي المستحقات</div>
                            </div>
                        </div>
                    </div>

                    ${purchases.length > 0 ? `
                    <div class="section">
                        <div class="section-title">
                            📦 سجل المشتريات (${purchases.length} مشترى)
                        </div>
                        <table class="purchases-table">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>عدد الأصناف</th>
                                    <th>الحالة</th>
                                    <th>الوصف</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${purchases.map(purchase => `
                                    <tr>
                                        <td>${purchase.invoice}</td>
                                        <td>${purchase.date}</td>
                                        <td>${purchase.amount.toLocaleString()} ر.س</td>
                                        <td>${purchase.items}</td>
                                        <td>
                                            <span class="status-badge status-${purchase.status === 'مستلم' ? 'received' : purchase.status === 'معتمد' ? 'approved' : 'pending'}">
                                                ${purchase.status}
                                            </span>
                                        </td>
                                        <td>${purchase.description || '-'}</td>
                                    </tr>
                                `).join('')}
                                <tr class="total-row">
                                    <td colspan="2"><strong>الإجمالي</strong></td>
                                    <td><strong>${totalPurchases.toLocaleString()} ر.س</strong></td>
                                    <td><strong>${purchases.reduce((sum, p) => sum + p.items, 0)}</strong></td>
                                    <td colspan="2"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    ` : ''}

                    <div class="footer">
                        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المحاسبة</p>
                        <p>تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>
                    </div>
                </body>
                </html>
            `;
        }

        // دالة إنشاء محتوى PDF
        function createPDFSupplierReport(supplier, purchases) {
            // نفس المحتوى مع تحسينات للـ PDF
            return createPrintableSupplierReport(supplier, purchases);
        }

        // معالجة بيانات التقرير اليومي
        function processDailyReportData(data) {
            const internalTotal = parseFloat(data.cash_sales || 0) + parseFloat(data.card_sales || 0);

            const externalSales = {
                hungerstation: {
                    amount: parseFloat(data.hungerstation_amount || 0),
                    orders: parseInt(data.hungerstation_orders || 0),
                    commission: parseFloat(data.hungerstation_commission || 0)
                },
                talabat: {
                    amount: parseFloat(data.talabat_amount || 0),
                    orders: parseInt(data.talabat_orders || 0),
                    commission: parseFloat(data.talabat_commission || 0)
                },
                jahez: {
                    amount: parseFloat(data.jahez_amount || 0),
                    orders: parseInt(data.jahez_orders || 0),
                    commission: parseFloat(data.jahez_commission || 0)
                },
                mrsool: {
                    amount: parseFloat(data.mrsool_amount || 0),
                    orders: parseInt(data.mrsool_orders || 0),
                    commission: parseFloat(data.mrsool_commission || 0)
                }
            };

            const externalTotal = Object.values(externalSales).reduce((sum, platform) => sum + platform.amount, 0);
            const totalSales = internalTotal + externalTotal;
            const totalCommissions = Object.values(externalSales).reduce((sum, platform) => sum + platform.commission, 0);

            return {
                date: data.date,
                totalSales: totalSales,
                internalSales: {
                    cash: parseFloat(data.cash_sales || 0),
                    card: parseFloat(data.card_sales || 0),
                    total: internalTotal
                },
                externalSales: externalSales,
                itemizedSales: [], // سيتم إضافتها لاحقاً
                purchases: 0, // سيتم حسابها من المشتريات
                operatingCosts: totalCommissions,
                netProfit: totalSales - totalCommissions // تقدير أولي
            };
        }

        // تحميل لوحة التحكم عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء تحميل النظام...');

            // التحقق من وجود الدوال المهمة
            if (typeof showSection !== 'function') {
                console.error('❌ دالة showSection غير موجودة!');
                return;
            }

            // استرداد البيانات المحفوظة أولاً
            const dataLoaded = loadDataFromLocalStorage();
            if (dataLoaded) {
                console.log('✅ تم استرداد البيانات المحفوظة بنجاح');
            } else {
                console.log('📝 بدء بالبيانات الافتراضية');
                // حفظ البيانات الافتراضية للمرة الأولى
                saveDataToLocalStorage();
            }

            // عرض لوحة التحكم
            try {
                showSection('dashboard');
                console.log('✅ تم عرض لوحة التحكم بنجاح');
            } catch (error) {
                console.error('❌ خطأ في عرض لوحة التحكم:', error);
            }

            // تفعيل العنصر الأول في الشريط الجانبي
            const firstSidebarItem = document.querySelector('.sidebar-item');
            if (firstSidebarItem) {
                firstSidebarItem.classList.add('active');
            }

            // إضافة مستمعات البحث
            addSearchListeners();
            // بدء تحديث التاريخ والوقت
            startDateTimeUpdater();

            // إضافة رسائل مساعدة في وحدة التحكم
            console.log('=== نظام المحاسبة جاهز مع نظام الفواتير والأرصدة ===');
            console.log('🧪 للاختبار العادي: testAddData()');
            console.log('⚡ للاختبار المباشر: testDirectSave()');
            console.log('📝 لاختبار النموذج: testFormModal()');
            console.log('👁️ لاختبار عرض المورد: testViewSupplier()');
            console.log('💾 لاختبار الحفظ والاسترداد: testSaveLoad()');
            console.log('🔄 لاختبار جميع الأقسام: testSections()');
            console.log('🔢 لاختبار نظام الفواتير المخصص: testInvoiceSystem()');
            console.log('🏷️ لاختبار إنشاء رموز الموردين: testSupplierPrefixGeneration()');
            console.log('📦 لاختبار إضافة مشترى تلقائي: testAutoPurchase()');
            console.log('💰 لاختبار حساب الأرصدة: testBalanceCalculation()');
            console.log('🎨 لاختبار واجهة الموردين: testSuppliersInterface()');
            console.log('🔢 لاختبار معالجة الأرقام: testNumberProcessing()');
            console.log('📊 لاختبار تحسين جدول المشتريات: testPurchasesTableImprovement()');
            console.log('✨ لاختبار تأثيرات التمرير: testHoverEffects()');
            console.log('🎯 لاختبار إصلاح المضاعفة: testAmountMultiplicationFix()');
            console.log('🔍 للتشخيص: diagnoseSystem()');
            console.log('💾 لحفظ البيانات: saveDataToLocalStorage()');
            console.log('📂 لاسترداد البيانات: loadDataFromLocalStorage()');
            console.log('📥 لتصدير البيانات: exportAllData()');
            console.log('🗑️ لحذف جميع البيانات: resetAllData()');
            console.log('📊 لمراقبة الأخطاء: افتح تبويب Console');
            console.log('💡 نصيحة: بطاقات الموردين لها تأثير مخصص (إطار أزرق فقط)!');
            console.log('✅ تم تخصيص تأثيرات التمرير حسب كل قسم');
            console.log('=========================================================');
        });
    </script>
</body>
</html>
