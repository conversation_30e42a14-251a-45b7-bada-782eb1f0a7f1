<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة الشامل</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
        }
        .card-gradient {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%);
        }
        .sidebar-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-radius: 8px;
            transition: all 0.2s;
            cursor: pointer;
        }
        .sidebar-item:hover {
            background-color: #f3f4f6;
        }
        .sidebar-item.active {
            background-color: #3b82f6;
            color: white;
        }
        .dark {
            background-color: #1f2937 !important;
            color: #f9fafb !important;
        }
        .dark .card-gradient {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%) !important;
        }
        .dark .bg-white {
            background-color: #374151 !important;
            color: #f9fafb !important;
        }
        .dark .bg-gray-50 {
            background-color: #4b5563 !important;
        }
        .dark .border-gray-200 {
            border-color: #6b7280 !important;
        }
        .dark .text-gray-600 {
            color: #d1d5db !important;
        }
        .dark .text-gray-500 {
            color: #9ca3af !important;
        }
        .dark .hover\\:bg-gray-50:hover {
            background-color: #4b5563 !important;
        }
        .dark .hover\\:bg-gray-100:hover {
            background-color: #4b5563 !important;
        }

        /* تحسين الأزرار */
        .btn {
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        /* تحسين الجداول */
        .table-row:hover {
            background-color: #f8fafc;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }
        .dark .table-row:hover {
            background-color: #4b5563 !important;
        }

        /* تحسين النماذج */
        .form-input {
            transition: all 0.2s ease;
        }
        .form-input:focus {
            transform: scale(1.02);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* تحسين البطاقات */
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        /* تحسين الأيقونات */
        .icon-btn {
            padding: 8px;
            border-radius: 6px;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .icon-btn:hover {
            background-color: #e5e7eb;
            transform: scale(1.1);
        }
        .dark .icon-btn:hover {
            background-color: #4b5563 !important;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- الشريط الجانبي -->
        <div class="w-64 bg-white border-l border-gray-200 shadow-sm">
            <!-- شعار التطبيق -->
            <div class="flex h-16 items-center justify-center border-b border-gray-200">
                <div class="flex items-center gap-2">
                    <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600 text-white">
                        📊
                    </div>
                    <span class="text-lg font-bold">نظام المحاسبة</span>
                </div>
            </div>

            <!-- قائمة التنقل -->
            <nav class="p-4 space-y-1">
                <div class="sidebar-item active" onclick="showSection('dashboard')">
                    <span>🏠</span>
                    <span class="font-medium">لوحة التحكم</span>
                </div>
                <div class="sidebar-item" onclick="showSection('purchases')">
                    <span>🛒</span>
                    <span class="font-medium">المشتريات</span>
                </div>
                <div class="sidebar-item" onclick="showSection('payments')">
                    <span>💳</span>
                    <span class="font-medium">المدفوعات</span>
                </div>
                <div class="sidebar-item" onclick="showSection('suppliers')">
                    <span>🏢</span>
                    <span class="font-medium">الموردين</span>
                </div>
                <div class="sidebar-item" onclick="showSection('employees')">
                    <span>👥</span>
                    <span class="font-medium">الموظفين</span>
                </div>
                <div class="sidebar-item" onclick="showSection('expenses')">
                    <span>💰</span>
                    <span class="font-medium">المصروفات اليومية</span>
                </div>
                <div class="sidebar-item" onclick="showSection('sales')">
                    <span>📈</span>
                    <span class="font-medium">إدخال المبيعات</span>
                </div>
                <div class="sidebar-item" onclick="showSection('settings')">
                    <span>⚙️</span>
                    <span class="font-medium">الإعدادات</span>
                </div>
            </nav>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- الهيدر -->
            <header class="flex h-16 items-center justify-between border-b border-gray-200 bg-white px-6">
                <div class="flex items-center gap-4">
                    <div class="relative">
                        <input
                            type="text"
                            placeholder="البحث في النظام..."
                            class="h-10 w-80 rounded-lg border border-gray-300 bg-white pr-10 pl-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                        <span class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400">🔍</span>
                    </div>
                </div>

                <div class="flex items-center gap-2">
                    <button class="p-2 rounded-lg hover:bg-gray-100" onclick="toggleTheme()">
                        <span id="theme-icon">🌙</span>
                    </button>
                    <button class="p-2 rounded-lg hover:bg-gray-100 relative">
                        <span>🔔</span>
                        <span class="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-xs text-white">3</span>
                    </button>
                    <div class="mr-4 text-left">
                        <p class="text-sm font-medium" id="current-date">اليوم: جاري التحميل...</p>
                        <p class="text-xs text-gray-500" id="hijri-date">التاريخ الهجري: جاري التحميل...</p>
                        <p class="text-xs text-gray-500" id="last-update">آخر تحديث: جاري التحميل...</p>
                    </div>
                </div>
            </header>

            <!-- محتوى الصفحة -->
            <main class="flex-1 overflow-y-auto p-6" id="main-content">
                <!-- سيتم تحميل المحتوى هنا -->
            </main>
        </div>
    </div>

    <script>
        // متغير لتتبع القسم الحالي
        let currentActiveSection = 'dashboard';

        // بيانات وهمية
        const mockData = {
            kpis: [
                { title: 'إجمالي الإيرادات', value: '125,000 ر.س', change: '+12.5%', icon: '💰' },
                { title: 'إجمالي المبيعات', value: '1,234', change: '+8.2%', icon: '🛒' },
                { title: 'العملاء النشطين', value: '456', change: '+5.1%', icon: '👥' },
                { title: 'المخزون المتاح', value: '2,890', change: '-2.3%', icon: '📦' }
            ],
            recentActivities: [
                { title: 'مبيعة جديدة', description: 'تم بيع 5 قطع من المنتج A', amount: '2,500 ر.س', time: 'منذ 15 دقيقة' },
                { title: 'دفعة للمورد', description: 'تم دفع مستحقات شركة التوريد المحدودة', amount: '15,000 ر.س', time: 'منذ 45 دقيقة' },
                { title: 'تنبيه مخزون', description: 'المنتج B أوشك على النفاد (5 قطع متبقية)', time: 'منذ ساعتان' }
            ],
            purchases: [
                { id: 1, supplier: 'شركة التوريد المحدودة', invoice: 'INV-2024-001', date: '2024-01-15', amount: 15000, status: 'مستلم', items: 25 },
                { id: 2, supplier: 'مؤسسة الجودة التجارية', invoice: 'INV-2024-002', date: '2024-01-14', amount: 8500, status: 'معتمد', items: 12 },
                { id: 3, supplier: 'شركة التقنية المتقدمة', invoice: 'INV-2024-003', date: '2024-01-13', amount: 22000, status: 'في الانتظار', items: 8 }
            ],
            suppliers: [
                { id: 1, name: 'شركة التوريد المحدودة', contact: 'أحمد محمد', phone: '+966501234567', email: '<EMAIL>', city: 'الرياض', totalPurchases: 150000, balance: 25000, rating: 4.5 },
                { id: 2, name: 'مؤسسة الجودة التجارية', contact: 'فاطمة أحمد', phone: '+966507654321', email: '<EMAIL>', city: 'جدة', totalPurchases: 85000, balance: 12000, rating: 4.2 },
                { id: 3, name: 'شركة التقنية المتقدمة', contact: 'محمد علي', phone: '+966509876543', email: '<EMAIL>', city: 'الدمام', totalPurchases: 220000, balance: 0, rating: 4.8 }
            ],
            employees: [
                { id: 1, name: 'أحمد محمد علي', position: 'مدير المبيعات', department: 'المبيعات', phone: '+966501234567', email: '<EMAIL>', salary: 12000, status: 'نشط', employeeId: 'EMP-001' },
                { id: 2, name: 'فاطمة أحمد السالم', position: 'محاسبة', department: 'المالية', phone: '+966509876543', email: '<EMAIL>', salary: 9000, status: 'نشط', employeeId: 'EMP-002' },
                { id: 3, name: 'محمد علي الأحمد', position: 'مطور برمجيات', department: 'التقنية', phone: '+966502468135', email: '<EMAIL>', salary: 15000, status: 'في إجازة', employeeId: 'EMP-003' }
            ],
            expenses: [
                { id: 1, description: 'فاتورة الكهرباء - يناير', amount: 2500, date: '2024-01-15', category: 'مرافق', method: 'تحويل بنكي', status: 'معتمد', addedBy: 'أحمد محمد' },
                { id: 2, description: 'وقود السيارات', amount: 800, date: '2024-01-14', category: 'مواصلات', method: 'نقدي', status: 'معتمد', addedBy: 'فاطمة أحمد' },
                { id: 3, description: 'مواد تنظيف المكتب', amount: 350, date: '2024-01-13', category: 'مكتب', method: 'نقدي', status: 'في الانتظار', addedBy: 'محمد علي' },
                { id: 4, description: 'اشتراك الإنترنت', amount: 500, date: '2024-01-12', category: 'تقنية', method: 'تحويل بنكي', status: 'معتمد', addedBy: 'أحمد محمد' }
            ],
            payments: [
                { id: 1, recipient: 'شركة التوريد المحدودة', amount: 15000, date: '2024-01-15', dueDate: '2024-01-20', method: 'تحويل بنكي', status: 'مدفوع', category: 'موردين', description: 'دفعة مستحقات شهر ديسمبر', reference: 'TXN-001' },
                { id: 2, recipient: 'شركة الكهرباء', amount: 2500, date: '2024-01-14', dueDate: '2024-01-25', method: 'تحويل بنكي', status: 'معلق', category: 'مرافق', description: 'فاتورة الكهرباء - يناير' },
                { id: 3, recipient: 'أحمد محمد - موظف', amount: 8000, date: '2024-01-13', dueDate: '2024-01-15', method: 'نقدي', status: 'متأخر', category: 'رواتب', description: 'راتب شهر ديسمبر' },
                { id: 4, recipient: 'شركة التأمين', amount: 3500, date: '2024-01-12', dueDate: '2024-01-30', method: 'شيك', status: 'مدفوع', category: 'تأمين', description: 'قسط التأمين الشهري', reference: 'CHK-001' },
                { id: 5, recipient: 'مكتب المحاماة', amount: 5000, date: '2024-01-11', dueDate: '2024-01-28', method: 'تحويل بنكي', status: 'معلق', category: 'خدمات قانونية', description: 'استشارات قانونية' }
            ],
            sales: [
                { id: 1, invoice: 'INV-2024-001', customer: 'أحمد محمد', phone: '+966501234567', date: '2024-01-15', total: 402.5, method: 'نقدي', status: 'مكتمل', source: 'في المتجر', salesperson: 'فاطمة أحمد' },
                { id: 2, invoice: 'INV-2024-002', customer: 'سارة علي', date: '2024-01-14', total: 256, method: 'بطاقة', status: 'مكتمل', source: 'تطبيق توصيل', salesperson: 'محمد علي' },
                { id: 3, invoice: 'INV-2024-003', customer: 'خالد أحمد', phone: '+966509876543', date: '2024-01-13', total: 697.5, method: 'تقسيط', status: 'معلق', source: 'متجر إلكتروني', salesperson: 'أحمد محمد' }
            ]
        };

        // عرض لوحة التحكم
        function showDashboard() {
            return `
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold">مرحباً بك في نظام المحاسبة</h1>
                            <p class="text-gray-600">إليك ملخص سريع لأداء عملك اليوم</p>
                        </div>
                        <div class="text-left">
                            <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-lg shadow-lg">
                                <p class="text-sm opacity-90">الساعة الآن</p>
                                <p class="text-2xl font-bold" id="dashboard-clock">
                                    جاري التحميل...
                                </p>
                                <p class="text-xs opacity-75" id="dashboard-hijri">
                                    التاريخ الهجري
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- بطاقات المؤشرات الرئيسية -->
                    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        ${mockData.kpis.map(kpi => `
                            <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">${kpi.title}</p>
                                        <p class="text-2xl font-bold">${kpi.value}</p>
                                        <p class="text-xs text-green-600">${kpi.change} من الشهر الماضي</p>
                                    </div>
                                    <div class="text-2xl">${kpi.icon}</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>

                    <!-- الرسوم البيانية -->
                    <div class="grid gap-6 md:grid-cols-2">
                        <div class="col-span-2 rounded-lg border bg-white p-6 shadow-sm">
                            <h3 class="text-lg font-semibold mb-4">الإيرادات والمصروفات الشهرية</h3>
                            <div class="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
                                <div class="text-center">
                                    <div class="text-4xl mb-4">📊</div>
                                    <p class="text-lg font-medium">رسم بياني للإيرادات والمصروفات</p>
                                    <p class="text-sm text-gray-500 mt-2">سيتم عرض الرسوم البيانية التفاعلية هنا</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأنشطة الحديثة -->
                    <div class="grid gap-6 lg:grid-cols-3">
                        <div class="lg:col-span-2 rounded-lg border bg-white p-6 shadow-sm">
                            <h3 class="text-lg font-semibold mb-4">آخر الأنشطة</h3>
                            <div class="space-y-4">
                                ${mockData.recentActivities.map(activity => `
                                    <div class="flex items-start gap-3">
                                        <div class="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                                            📝
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-center justify-between">
                                                <p class="text-sm font-medium">${activity.title}</p>
                                                <span class="text-xs text-gray-500">${activity.time}</span>
                                            </div>
                                            <p class="text-sm text-gray-600">${activity.description}</p>
                                            ${activity.amount ? `<p class="text-sm font-medium text-blue-600">${activity.amount}</p>` : ''}
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <div class="rounded-lg border bg-white p-6 shadow-sm">
                            <h3 class="text-lg font-semibold mb-4">الإجراءات السريعة</h3>
                            <div class="grid grid-cols-3 gap-3">
                                <button onclick="addNewItem('sale')" class="btn flex flex-col items-center gap-2 p-4 border rounded-lg hover:bg-gray-50 card-hover">
                                    <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-green-500 text-white">➕</div>
                                    <div class="text-center">
                                        <p class="text-sm font-medium">إضافة مبيعة</p>
                                        <p class="text-xs text-gray-500">تسجيل معاملة جديدة</p>
                                    </div>
                                </button>
                                <button onclick="addNewItem('purchase')" class="btn flex flex-col items-center gap-2 p-4 border rounded-lg hover:bg-gray-50 card-hover">
                                    <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-500 text-white">🛒</div>
                                    <div class="text-center">
                                        <p class="text-sm font-medium">طلب شراء</p>
                                        <p class="text-xs text-gray-500">إنشاء طلب جديد</p>
                                    </div>
                                </button>
                                <button onclick="testAddData()" class="btn flex flex-col items-center gap-2 p-4 border rounded-lg hover:bg-gray-50 card-hover">
                                    <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-500 text-white">🧪</div>
                                    <div class="text-center">
                                        <p class="text-sm font-medium">اختبار البيانات</p>
                                        <p class="text-xs text-gray-500">إضافة بيانات تجريبية</p>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // عرض وحدة المشتريات
        function showPurchases() {
            const totalAmount = mockData.purchases.reduce((sum, p) => sum + p.amount, 0);
            const pendingCount = mockData.purchases.filter(p => p.status === 'في الانتظار').length;
            const receivedCount = mockData.purchases.filter(p => p.status === 'مستلم').length;

            return `
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold">إدارة المشتريات</h1>
                            <p class="text-gray-600">تتبع وإدارة جميع عمليات الشراء والموردين</p>
                        </div>
                        <button onclick="addNewItem('purchase')" class="btn bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
                            ➕ طلب شراء جديد
                        </button>
                    </div>

                    <!-- بطاقات الإحصائيات -->
                    <div class="grid gap-4 md:grid-cols-4">
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي المشتريات</p>
                                    <p class="text-2xl font-bold">${totalAmount.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">هذا الشهر</p>
                                </div>
                                <div class="text-2xl">💰</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">طلبات معلقة</p>
                                    <p class="text-2xl font-bold">${pendingCount}</p>
                                    <p class="text-xs text-gray-500">تحتاج موافقة</p>
                                </div>
                                <div class="text-2xl">⏳</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">طلبات مستلمة</p>
                                    <p class="text-2xl font-bold">${receivedCount}</p>
                                    <p class="text-xs text-gray-500">تم الاستلام</p>
                                </div>
                                <div class="text-2xl">📦</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">عدد الموردين</p>
                                    <p class="text-2xl font-bold">${mockData.suppliers.length}</p>
                                    <p class="text-xs text-gray-500">مورد نشط</p>
                                </div>
                                <div class="text-2xl">🏢</div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول المشتريات -->
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold">قائمة المشتريات</h3>
                                <div class="flex items-center gap-2">
                                    <input type="text" placeholder="البحث في المشتريات..." class="px-3 py-2 border rounded-lg text-sm">
                                    <button class="px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">🔍</button>
                                    <button onclick="forceReloadSection('purchases')" class="px-3 py-2 border rounded-lg text-sm hover:bg-gray-50" title="إعادة تحميل">🔄</button>
                                </div>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="text-right p-4 font-medium">رقم الفاتورة</th>
                                        <th class="text-right p-4 font-medium">المورد</th>
                                        <th class="text-right p-4 font-medium">التاريخ</th>
                                        <th class="text-right p-4 font-medium">المبلغ</th>
                                        <th class="text-right p-4 font-medium">عدد الأصناف</th>
                                        <th class="text-right p-4 font-medium">الحالة</th>
                                        <th class="text-right p-4 font-medium">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${mockData.purchases.map(purchase => `
                                        <tr class="table-row border-b hover:bg-gray-50">
                                            <td class="p-4 font-medium">${purchase.invoice}</td>
                                            <td class="p-4">${purchase.supplier}</td>
                                            <td class="p-4">${purchase.date}</td>
                                            <td class="p-4 font-medium">${purchase.amount.toLocaleString()} ر.س</td>
                                            <td class="p-4">${purchase.items}</td>
                                            <td class="p-4">
                                                <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                                    purchase.status === 'مستلم' ? 'bg-green-100 text-green-800' :
                                                    purchase.status === 'معتمد' ? 'bg-blue-100 text-blue-800' :
                                                    'bg-yellow-100 text-yellow-800'
                                                }">
                                                    ${purchase.status}
                                                </span>
                                            </td>
                                            <td class="p-4">
                                                <div class="flex items-center gap-2">
                                                    <button onclick="viewItem('purchase', ${purchase.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="عرض التفاصيل">👁️</button>
                                                    <button onclick="editItem('purchase', ${purchase.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="تعديل">✏️</button>
                                                    <button onclick="deleteItem('purchase', ${purchase.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="حذف">🗑️</button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        }

        // عرض وحدة الموردين
        function showSuppliers() {
            const totalSuppliers = mockData.suppliers.length;
            const totalPurchases = mockData.suppliers.reduce((sum, s) => sum + s.totalPurchases, 0);
            const totalOutstanding = mockData.suppliers.reduce((sum, s) => sum + s.balance, 0);
            const avgRating = (mockData.suppliers.reduce((sum, s) => sum + s.rating, 0) / totalSuppliers).toFixed(1);

            return `
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold">إدارة الموردين</h1>
                            <p class="text-gray-600">إدارة معلومات الموردين وتتبع المعاملات التجارية</p>
                        </div>
                        <button onclick="addNewItem('supplier')" class="btn bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
                            ➕ مورد جديد
                        </button>
                    </div>

                    <!-- بطاقات الإحصائيات -->
                    <div class="grid gap-4 md:grid-cols-4">
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي الموردين</p>
                                    <p class="text-2xl font-bold">${totalSuppliers}</p>
                                    <p class="text-xs text-gray-500">${totalSuppliers} نشط</p>
                                </div>
                                <div class="text-2xl">🏢</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي المشتريات</p>
                                    <p class="text-2xl font-bold">${totalPurchases.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">جميع الموردين</p>
                                </div>
                                <div class="text-2xl">💰</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">أرصدة مستحقة</p>
                                    <p class="text-2xl font-bold">${totalOutstanding.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">مبالغ مستحقة</p>
                                </div>
                                <div class="text-2xl">📊</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">متوسط التقييم</p>
                                    <p class="text-2xl font-bold">${avgRating}</p>
                                    <p class="text-xs text-gray-500">من 5 نجوم</p>
                                </div>
                                <div class="text-2xl">⭐</div>
                            </div>
                        </div>
                    </div>

                    <!-- بطاقات الموردين -->
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold">قائمة الموردين</h3>
                                <div class="flex items-center gap-2">
                                    <input type="text" placeholder="البحث في الموردين..." class="px-3 py-2 border rounded-lg text-sm">
                                    <button class="px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">🔍</button>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                ${mockData.suppliers.map(supplier => `
                                    <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                                        <div class="flex items-start justify-between mb-3">
                                            <div>
                                                <h4 class="text-lg font-semibold">${supplier.name}</h4>
                                                <p class="text-sm text-gray-600">${supplier.contact}</p>
                                            </div>
                                            <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-green-100 text-green-800">
                                                نشط
                                            </span>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="flex items-center gap-2 text-sm">
                                                <span>📞</span>
                                                <span>${supplier.phone}</span>
                                            </div>
                                            <div class="flex items-center gap-2 text-sm">
                                                <span>📧</span>
                                                <span class="truncate">${supplier.email}</span>
                                            </div>
                                            <div class="flex items-center gap-2 text-sm">
                                                <span>📍</span>
                                                <span>${supplier.city}</span>
                                            </div>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="flex justify-between text-sm">
                                                <span class="text-gray-600">إجمالي المشتريات:</span>
                                                <span class="font-medium">${supplier.totalPurchases.toLocaleString()} ر.س</span>
                                            </div>
                                            <div class="flex justify-between text-sm">
                                                <span class="text-gray-600">رصيد مستحق:</span>
                                                <span class="font-medium">${supplier.balance.toLocaleString()} ر.س</span>
                                            </div>
                                            <div class="flex justify-between text-sm">
                                                <span class="text-gray-600">التقييم:</span>
                                                <span class="font-medium">⭐ ${supplier.rating}</span>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <button onclick="viewItem('supplier', ${supplier.id})" class="btn flex-1 px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">👁️ عرض</button>
                                            <button onclick="editItem('supplier', ${supplier.id})" class="btn flex-1 px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">✏️ تعديل</button>
                                            <button onclick="deleteItem('supplier', ${supplier.id})" class="btn px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">🗑️</button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // عرض وحدة الموظفين
        function showEmployees() {
            const totalEmployees = mockData.employees.length;
            const activeEmployees = mockData.employees.filter(e => e.status === 'نشط').length;
            const totalSalaries = mockData.employees.reduce((sum, e) => sum + e.salary, 0);
            const onLeaveCount = mockData.employees.filter(e => e.status === 'في إجازة').length;

            return `
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold">إدارة الموظفين</h1>
                            <p class="text-gray-600">إدارة معلومات الموظفين والرواتب والحضور</p>
                        </div>
                        <button onclick="addNewItem('employee')" class="btn bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
                            ➕ موظف جديد
                        </button>
                    </div>

                    <!-- بطاقات الإحصائيات -->
                    <div class="grid gap-4 md:grid-cols-4">
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي الموظفين</p>
                                    <p class="text-2xl font-bold">${totalEmployees}</p>
                                    <p class="text-xs text-gray-500">${activeEmployees} نشط</p>
                                </div>
                                <div class="text-2xl">👥</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي الرواتب</p>
                                    <p class="text-2xl font-bold">${totalSalaries.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">شهرياً</p>
                                </div>
                                <div class="text-2xl">💰</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">موظفين في إجازة</p>
                                    <p class="text-2xl font-bold">${onLeaveCount}</p>
                                    <p class="text-xs text-gray-500">حالياً</p>
                                </div>
                                <div class="text-2xl">🏖️</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">متوسط الراتب</p>
                                    <p class="text-2xl font-bold">${Math.round(totalSalaries / totalEmployees).toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">للموظف الواحد</p>
                                </div>
                                <div class="text-2xl">📊</div>
                            </div>
                        </div>
                    </div>

                    <!-- بطاقات الموظفين -->
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold">قائمة الموظفين</h3>
                                <div class="flex items-center gap-2">
                                    <input type="text" placeholder="البحث في الموظفين..." class="px-3 py-2 border rounded-lg text-sm">
                                    <button class="px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">🔍</button>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                ${mockData.employees.map(employee => `
                                    <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                                        <div class="flex items-start justify-between mb-3">
                                            <div>
                                                <h4 class="text-lg font-semibold">${employee.name}</h4>
                                                <p class="text-sm text-gray-600">${employee.position}</p>
                                                <p class="text-xs text-gray-500">${employee.department}</p>
                                            </div>
                                            <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                                employee.status === 'نشط' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                                            }">
                                                ${employee.status}
                                            </span>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="flex items-center gap-2 text-sm">
                                                <span>📞</span>
                                                <span>${employee.phone}</span>
                                            </div>
                                            <div class="flex items-center gap-2 text-sm">
                                                <span>📧</span>
                                                <span class="truncate">${employee.email}</span>
                                            </div>
                                            <div class="flex items-center gap-2 text-sm">
                                                <span>🆔</span>
                                                <span>${employee.employeeId}</span>
                                            </div>
                                        </div>
                                        <div class="space-y-2 mb-4">
                                            <div class="flex justify-between text-sm">
                                                <span class="text-gray-600">الراتب:</span>
                                                <span class="font-medium">${employee.salary.toLocaleString()} ر.س</span>
                                            </div>
                                            <div class="flex justify-between text-sm">
                                                <span class="text-gray-600">القسم:</span>
                                                <span class="font-medium">${employee.department}</span>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <button onclick="viewItem('employee', ${employee.id})" class="btn flex-1 px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">👁️ عرض</button>
                                            <button onclick="editItem('employee', ${employee.id})" class="btn flex-1 px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">✏️ تعديل</button>
                                            <button onclick="deleteItem('employee', ${employee.id})" class="btn px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">🗑️</button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // عرض وحدة المصروفات اليومية
        function showExpenses() {
            const totalExpenses = mockData.expenses.reduce((sum, e) => sum + e.amount, 0);
            const approvedExpenses = mockData.expenses.filter(e => e.status === 'معتمد').reduce((sum, e) => sum + e.amount, 0);
            const pendingCount = mockData.expenses.filter(e => e.status === 'في الانتظار').length;

            // تجميع المصروفات حسب الفئة
            const categories = ['مرافق', 'مواصلات', 'مكتب', 'تقنية'];
            const expensesByCategory = categories.map(category => ({
                category,
                amount: mockData.expenses.filter(e => e.category === category).reduce((sum, e) => sum + e.amount, 0),
                count: mockData.expenses.filter(e => e.category === category).length
            })).filter(item => item.count > 0);

            return `
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold">المصروفات اليومية</h1>
                            <p class="text-gray-600">تتبع وإدارة جميع المصروفات اليومية والتشغيلية</p>
                        </div>
                        <button onclick="addNewItem('expense')" class="btn bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
                            ➕ مصروف جديد
                        </button>
                    </div>

                    <!-- بطاقات الإحصائيات -->
                    <div class="grid gap-4 md:grid-cols-4">
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي المصروفات</p>
                                    <p class="text-2xl font-bold">${totalExpenses.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">هذا الشهر</p>
                                </div>
                                <div class="text-2xl">💰</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">مصروفات معتمدة</p>
                                    <p class="text-2xl font-bold">${approvedExpenses.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">تم اعتمادها</p>
                                </div>
                                <div class="text-2xl">✅</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">مصروفات معلقة</p>
                                    <p class="text-2xl font-bold">${pendingCount}</p>
                                    <p class="text-xs text-gray-500">تحتاج موافقة</p>
                                </div>
                                <div class="text-2xl">⏳</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">متوسط المصروف</p>
                                    <p class="text-2xl font-bold">${Math.round(totalExpenses / mockData.expenses.length).toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">للمصروف الواحد</p>
                                </div>
                                <div class="text-2xl">📊</div>
                            </div>
                        </div>
                    </div>

                    <!-- المصروفات حسب الفئة -->
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <h3 class="text-lg font-semibold">المصروفات حسب الفئة</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                                ${expensesByCategory.map(item => `
                                    <div class="flex items-center justify-between p-3 border rounded-lg">
                                        <div>
                                            <p class="font-medium">${item.category}</p>
                                            <p class="text-sm text-gray-500">${item.count} مصروف</p>
                                        </div>
                                        <div class="text-left">
                                            <p class="font-bold">${item.amount.toLocaleString()} ر.س</p>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>

                    <!-- جدول المصروفات -->
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold">قائمة المصروفات</h3>
                                <div class="flex items-center gap-2">
                                    <select class="px-3 py-2 border rounded-lg text-sm">
                                        <option>جميع الفئات</option>
                                        <option>مرافق</option>
                                        <option>مواصلات</option>
                                        <option>مكتب</option>
                                        <option>تقنية</option>
                                    </select>
                                    <input type="text" placeholder="البحث في المصروفات..." class="px-3 py-2 border rounded-lg text-sm">
                                    <button class="px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">🔍</button>
                                </div>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="text-right p-4 font-medium">الوصف</th>
                                        <th class="text-right p-4 font-medium">المبلغ</th>
                                        <th class="text-right p-4 font-medium">التاريخ</th>
                                        <th class="text-right p-4 font-medium">الفئة</th>
                                        <th class="text-right p-4 font-medium">طريقة الدفع</th>
                                        <th class="text-right p-4 font-medium">الحالة</th>
                                        <th class="text-right p-4 font-medium">مضاف بواسطة</th>
                                        <th class="text-right p-4 font-medium">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${mockData.expenses.map(expense => `
                                        <tr class="border-b hover:bg-gray-50">
                                            <td class="p-4 font-medium">${expense.description}</td>
                                            <td class="p-4 font-medium">${expense.amount.toLocaleString()} ر.س</td>
                                            <td class="p-4">${expense.date}</td>
                                            <td class="p-4">${expense.category}</td>
                                            <td class="p-4">${expense.method}</td>
                                            <td class="p-4">
                                                <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                                    expense.status === 'معتمد' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                                                }">
                                                    ${expense.status}
                                                </span>
                                            </td>
                                            <td class="p-4">${expense.addedBy}</td>
                                            <td class="p-4">
                                                <div class="flex items-center gap-2">
                                                    <button onclick="viewItem('expense', ${expense.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="عرض التفاصيل">👁️</button>
                                                    <button onclick="editItem('expense', ${expense.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="تعديل">✏️</button>
                                                    <button onclick="deleteItem('expense', ${expense.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="حذف">🗑️</button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        }

        // عرض وحدة إدخال المبيعات
        function showSales() {
            const totalSales = mockData.sales.reduce((sum, s) => sum + s.total, 0);
            const completedSales = mockData.sales.filter(s => s.status === 'مكتمل');
            const totalCompleted = completedSales.reduce((sum, s) => sum + s.total, 0);
            const pendingCount = mockData.sales.filter(s => s.status === 'معلق').length;
            const averageSale = completedSales.length > 0 ? totalCompleted / completedSales.length : 0;

            // تجميع المبيعات حسب المصدر
            const sources = ['في المتجر', 'تطبيق توصيل', 'متجر إلكتروني'];
            const salesBySource = sources.map(source => ({
                source,
                count: mockData.sales.filter(s => s.source === source).length,
                total: mockData.sales.filter(s => s.source === source).reduce((sum, s) => sum + s.total, 0)
            })).filter(item => item.count > 0);

            return `
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold">إدخال المبيعات</h1>
                            <p class="text-gray-600">تسجيل وإدارة جميع المبيعات من مختلف المصادر</p>
                        </div>
                        <button onclick="addNewItem('sale')" class="btn bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
                            ➕ مبيعة جديدة
                        </button>
                    </div>

                    <!-- بطاقات الإحصائيات -->
                    <div class="grid gap-4 md:grid-cols-4">
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي المبيعات</p>
                                    <p class="text-2xl font-bold">${totalSales.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">هذا الشهر</p>
                                </div>
                                <div class="text-2xl">📈</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">مبيعات مكتملة</p>
                                    <p class="text-2xl font-bold">${totalCompleted.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">${completedSales.length} معاملة</p>
                                </div>
                                <div class="text-2xl">✅</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">مبيعات معلقة</p>
                                    <p class="text-2xl font-bold">${pendingCount}</p>
                                    <p class="text-xs text-gray-500">تحتاج متابعة</p>
                                </div>
                                <div class="text-2xl">⏳</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">متوسط المبيعة</p>
                                    <p class="text-2xl font-bold">${Math.round(averageSale).toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">للمعاملة الواحدة</p>
                                </div>
                                <div class="text-2xl">📊</div>
                            </div>
                        </div>
                    </div>

                    <!-- المبيعات حسب المصدر -->
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <h3 class="text-lg font-semibold">المبيعات حسب المصدر</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                ${salesBySource.map(item => `
                                    <div class="flex items-center justify-between p-3 border rounded-lg">
                                        <div class="flex items-center gap-2">
                                            <span>${item.source === 'في المتجر' ? '🏪' : item.source === 'تطبيق توصيل' ? '🚚' : '🌐'}</span>
                                            <div>
                                                <p class="font-medium">${item.source}</p>
                                                <p class="text-sm text-gray-500">${item.count} معاملة</p>
                                            </div>
                                        </div>
                                        <div class="text-left">
                                            <p class="font-bold">${item.total.toLocaleString()} ر.س</p>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>

                    <!-- جدول المبيعات -->
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold">قائمة المبيعات</h3>
                                <div class="flex items-center gap-2">
                                    <input type="text" placeholder="البحث في المبيعات..." class="px-3 py-2 border rounded-lg text-sm">
                                    <button class="px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">🔍</button>
                                </div>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="text-right p-4 font-medium">رقم الفاتورة</th>
                                        <th class="text-right p-4 font-medium">العميل</th>
                                        <th class="text-right p-4 font-medium">التاريخ</th>
                                        <th class="text-right p-4 font-medium">المبلغ الإجمالي</th>
                                        <th class="text-right p-4 font-medium">طريقة الدفع</th>
                                        <th class="text-right p-4 font-medium">المصدر</th>
                                        <th class="text-right p-4 font-medium">الحالة</th>
                                        <th class="text-right p-4 font-medium">موظف المبيعات</th>
                                        <th class="text-right p-4 font-medium">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${mockData.sales.map(sale => `
                                        <tr class="border-b hover:bg-gray-50">
                                            <td class="p-4 font-medium">${sale.invoice}</td>
                                            <td class="p-4">
                                                <div>
                                                    <p class="font-medium">${sale.customer}</p>
                                                    ${sale.phone ? `<p class="text-sm text-gray-500">${sale.phone}</p>` : ''}
                                                </div>
                                            </td>
                                            <td class="p-4">${sale.date}</td>
                                            <td class="p-4 font-medium">${sale.total.toLocaleString()} ر.س</td>
                                            <td class="p-4">${sale.method}</td>
                                            <td class="p-4">
                                                <div class="flex items-center gap-2">
                                                    <span>${sale.source === 'في المتجر' ? '🏪' : sale.source === 'تطبيق توصيل' ? '🚚' : '🌐'}</span>
                                                    <span>${sale.source}</span>
                                                </div>
                                            </td>
                                            <td class="p-4">
                                                <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                                    sale.status === 'مكتمل' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                                                }">
                                                    ${sale.status}
                                                </span>
                                            </td>
                                            <td class="p-4">${sale.salesperson}</td>
                                            <td class="p-4">
                                                <div class="flex items-center gap-2">
                                                    <button onclick="viewItem('sale', ${sale.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="عرض التفاصيل">👁️</button>
                                                    <button onclick="editItem('sale', ${sale.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="تعديل">✏️</button>
                                                    <button onclick="deleteItem('sale', ${sale.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="حذف">🗑️</button>
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        }

        // عرض وحدة المدفوعات
        function showPayments() {
            const totalPaid = mockData.payments.filter(p => p.status === 'مدفوع').reduce((sum, p) => sum + p.amount, 0);
            const totalPending = mockData.payments.filter(p => p.status === 'معلق').reduce((sum, p) => sum + p.amount, 0);
            const overdueCount = mockData.payments.filter(p => p.status === 'متأخر').length;
            const totalPayments = mockData.payments.reduce((sum, p) => sum + p.amount, 0);

            return `
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-3xl font-bold">إدارة المدفوعات</h1>
                            <p class="text-gray-600">تتبع وإدارة جميع المدفوعات والالتزامات المالية</p>
                        </div>
                        <button onclick="addNewItem('payment')" class="btn bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2">
                            ➕ دفعة جديدة
                        </button>
                    </div>

                    <!-- بطاقات الإحصائيات -->
                    <div class="grid gap-4 md:grid-cols-4">
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي المدفوع</p>
                                    <p class="text-2xl font-bold">${totalPaid.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">هذا الشهر</p>
                                </div>
                                <div class="text-2xl">💳</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">مدفوعات معلقة</p>
                                    <p class="text-2xl font-bold">${totalPending.toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">في الانتظار</p>
                                </div>
                                <div class="text-2xl">⏳</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">مدفوعات متأخرة</p>
                                    <p class="text-2xl font-bold text-red-600">${overdueCount}</p>
                                    <p class="text-xs text-gray-500">تحتاج متابعة</p>
                                </div>
                                <div class="text-2xl">⚠️</div>
                            </div>
                        </div>
                        <div class="card-gradient rounded-lg border bg-white p-6 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">متوسط الدفعة</p>
                                    <p class="text-2xl font-bold">${Math.round(totalPayments / mockData.payments.length).toLocaleString()} ر.س</p>
                                    <p class="text-xs text-gray-500">للدفعة الواحدة</p>
                                </div>
                                <div class="text-2xl">📊</div>
                            </div>
                        </div>
                    </div>

                    <!-- المدفوعات حسب الفئة -->
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <h3 class="text-lg font-semibold">المدفوعات حسب الفئة</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                                ${['موردين', 'مرافق', 'رواتب', 'تأمين', 'خدمات قانونية'].map(category => {
                                    const categoryPayments = mockData.payments.filter(p => p.category === category);
                                    const categoryTotal = categoryPayments.reduce((sum, p) => sum + p.amount, 0);
                                    if (categoryPayments.length === 0) return '';
                                    return `
                                        <div class="flex items-center justify-between p-3 border rounded-lg">
                                            <div>
                                                <p class="font-medium">${category}</p>
                                                <p class="text-sm text-gray-500">${categoryPayments.length} دفعة</p>
                                            </div>
                                            <div class="text-left">
                                                <p class="font-bold">${categoryTotal.toLocaleString()} ر.س</p>
                                            </div>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                    </div>

                    <!-- جدول المدفوعات -->
                    <div class="rounded-lg border bg-white shadow-sm">
                        <div class="p-6 border-b">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold">قائمة المدفوعات</h3>
                                <div class="flex items-center gap-2">
                                    <input type="text" placeholder="البحث في المدفوعات..." class="px-3 py-2 border rounded-lg text-sm">
                                    <button class="px-3 py-2 border rounded-lg text-sm hover:bg-gray-50">🔍</button>
                                </div>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="text-right p-4 font-medium">المستفيد</th>
                                        <th class="text-right p-4 font-medium">المبلغ</th>
                                        <th class="text-right p-4 font-medium">تاريخ الدفع</th>
                                        <th class="text-right p-4 font-medium">تاريخ الاستحقاق</th>
                                        <th class="text-right p-4 font-medium">طريقة الدفع</th>
                                        <th class="text-right p-4 font-medium">الفئة</th>
                                        <th class="text-right p-4 font-medium">الحالة</th>
                                        <th class="text-right p-4 font-medium">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${mockData.payments.map(payment => `
                                        <tr class="border-b hover:bg-gray-50">
                                            <td class="p-4 font-medium">${payment.recipient}</td>
                                            <td class="p-4 font-medium">${payment.amount.toLocaleString()} ر.س</td>
                                            <td class="p-4">${payment.date}</td>
                                            <td class="p-4">${payment.dueDate}</td>
                                            <td class="p-4">${payment.method}</td>
                                            <td class="p-4">${payment.category}</td>
                                            <td class="p-4">
                                                <span class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                                    payment.status === 'مدفوع' ? 'bg-green-100 text-green-800' :
                                                    payment.status === 'معلق' ? 'bg-yellow-100 text-yellow-800' :
                                                    'bg-red-100 text-red-800'
                                                }">
                                                    ${payment.status}
                                                </span>
                                            </td>
                                            <td class="p-4">
                                                <div class="flex items-center gap-2">
                                                    <button onclick="viewItem('payment', ${payment.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="عرض التفاصيل">👁️</button>
                                                    <button onclick="editItem('payment', ${payment.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="تعديل">✏️</button>
                                                    <button onclick="deleteItem('payment', ${payment.id})" class="icon-btn p-1 hover:bg-gray-100 rounded" title="حذف">🗑️</button>
                                                    ${payment.status === 'معلق' ? '<button onclick="confirmPayment(' + payment.id + ')" class="icon-btn p-1 hover:bg-gray-100 rounded text-green-600" title="تأكيد الدفع">✅</button>' : ''}
                                                </div>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- تنبيهات المدفوعات المتأخرة -->
                    ${mockData.payments.filter(p => p.status === 'متأخر').length > 0 ? `
                        <div class="rounded-lg border border-red-200 bg-red-50 p-6">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="text-2xl">⚠️</div>
                                <div>
                                    <h3 class="text-lg font-semibold text-red-800">تنبيه: مدفوعات متأخرة</h3>
                                    <p class="text-sm text-red-600">يوجد ${mockData.payments.filter(p => p.status === 'متأخر').length} مدفوعات متأخرة تحتاج إلى متابعة فورية</p>
                                </div>
                            </div>
                            <div class="space-y-2">
                                ${mockData.payments.filter(p => p.status === 'متأخر').map(payment => `
                                    <div class="flex items-center justify-between p-3 bg-white rounded border">
                                        <div>
                                            <p class="font-medium">${payment.recipient}</p>
                                            <p class="text-sm text-gray-600">${payment.description}</p>
                                        </div>
                                        <div class="text-left">
                                            <p class="font-bold text-red-600">${payment.amount.toLocaleString()} ر.س</p>
                                            <p class="text-xs text-gray-500">مستحق: ${payment.dueDate}</p>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // عرض الأقسام المختلفة
        function showSection(section) {
            console.log('عرض القسم:', section);

            // حفظ القسم الحالي
            currentActiveSection = section;

            // إزالة الفئة النشطة من جميع العناصر
            document.querySelectorAll('.sidebar-item').forEach(item => {
                item.classList.remove('active');
            });

            // إضافة الفئة النشطة للعنصر المحدد
            if (event && event.target) {
                event.target.closest('.sidebar-item').classList.add('active');
            } else {
                // إذا لم يكن هناك event، ابحث عن العنصر المناسب وفعله
                const sectionNames = {
                    'dashboard': 'لوحة التحكم',
                    'purchases': 'المشتريات',
                    'payments': 'المدفوعات',
                    'suppliers': 'الموردين',
                    'employees': 'الموظفين',
                    'expenses': 'المصروفات اليومية',
                    'sales': 'إدخال المبيعات',
                    'settings': 'الإعدادات'
                };

                const sectionName = sectionNames[section];
                if (sectionName) {
                    const sidebarItems = document.querySelectorAll('.sidebar-item');
                    sidebarItems.forEach(item => {
                        if (item.textContent.includes(sectionName)) {
                            item.classList.add('active');
                        }
                    });
                }
            }

            const content = document.getElementById('main-content');

            switch(section) {
                case 'dashboard':
                    content.innerHTML = showDashboard();
                    break;
                case 'purchases':
                    content.innerHTML = showPurchases();
                    break;
                case 'payments':
                    content.innerHTML = showPayments();
                    break;
                case 'suppliers':
                    content.innerHTML = showSuppliers();
                    break;
                case 'employees':
                    content.innerHTML = showEmployees();
                    break;
                case 'expenses':
                    content.innerHTML = showExpenses();
                    break;
                case 'sales':
                    content.innerHTML = showSales();
                    break;
                case 'settings':
                    content.innerHTML = `
                        <div class="space-y-6">
                            <h1 class="text-3xl font-bold">الإعدادات</h1>
                            <div class="rounded-lg border bg-white p-6 shadow-sm">
                                <p class="text-center text-gray-500">لوحة الإعدادات - قيد التطوير</p>
                            </div>
                        </div>
                    `;
                    break;
            }
        }

        // تبديل الثيم
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('theme-icon');

            if (body.classList.contains('dark')) {
                body.classList.remove('dark');
                themeIcon.textContent = '🌙';
            } else {
                body.classList.add('dark');
                themeIcon.textContent = '☀️';
            }
        }

        // وظائف تفاعلية للأزرار
        function addNewItem(type) {
            const forms = {
                purchase: {
                    title: 'إضافة طلب شراء جديد',
                    fields: [
                        { name: 'supplier', label: 'المورد', type: 'select', options: mockData.suppliers.map(s => s.name) },
                        { name: 'invoice', label: 'رقم الفاتورة', type: 'text' },
                        { name: 'amount', label: 'المبلغ', type: 'number' },
                        { name: 'items', label: 'عدد الأصناف', type: 'number' },
                        { name: 'description', label: 'الوصف', type: 'textarea' }
                    ]
                },
                payment: {
                    title: 'إضافة دفعة جديدة',
                    fields: [
                        { name: 'recipient', label: 'المستفيد', type: 'text' },
                        { name: 'amount', label: 'المبلغ', type: 'number' },
                        { name: 'method', label: 'طريقة الدفع', type: 'select', options: ['نقدي', 'تحويل بنكي', 'شيك', 'بطاقة'] },
                        { name: 'category', label: 'الفئة', type: 'select', options: ['موردين', 'مرافق', 'رواتب', 'تأمين'] },
                        { name: 'description', label: 'الوصف', type: 'textarea' }
                    ]
                },
                supplier: {
                    title: 'إضافة مورد جديد',
                    fields: [
                        { name: 'name', label: 'اسم الشركة', type: 'text', required: true },
                        { name: 'contact', label: 'الشخص المسؤول', type: 'text', required: true },
                        { name: 'phone', label: 'رقم الهاتف', type: 'tel', required: true },
                        { name: 'email', label: 'البريد الإلكتروني', type: 'email', required: true },
                        { name: 'city', label: 'المدينة', type: 'text', required: false }
                    ]
                },
                employee: {
                    title: 'إضافة موظف جديد',
                    fields: [
                        { name: 'name', label: 'الاسم الكامل', type: 'text', required: true },
                        { name: 'position', label: 'المنصب', type: 'text', required: true },
                        { name: 'department', label: 'القسم', type: 'select', options: ['المبيعات', 'المالية', 'التقنية', 'الموارد البشرية'], required: true },
                        { name: 'phone', label: 'رقم الهاتف', type: 'tel', required: false },
                        { name: 'email', label: 'البريد الإلكتروني', type: 'email', required: false },
                        { name: 'salary', label: 'الراتب', type: 'number', required: true }
                    ]
                },
                expense: {
                    title: 'إضافة مصروف جديد',
                    fields: [
                        { name: 'description', label: 'الوصف', type: 'text' },
                        { name: 'amount', label: 'المبلغ', type: 'number' },
                        { name: 'category', label: 'الفئة', type: 'select', options: ['مرافق', 'مواصلات', 'مكتب', 'تقنية'] },
                        { name: 'method', label: 'طريقة الدفع', type: 'select', options: ['نقدي', 'تحويل بنكي', 'شيك', 'بطاقة'] }
                    ]
                },
                sale: {
                    title: 'إضافة مبيعة جديدة',
                    fields: [
                        { name: 'customer', label: 'اسم العميل', type: 'text' },
                        { name: 'phone', label: 'رقم الهاتف', type: 'tel' },
                        { name: 'total', label: 'المبلغ الإجمالي', type: 'number' },
                        { name: 'method', label: 'طريقة الدفع', type: 'select', options: ['نقدي', 'بطاقة', 'تحويل بنكي', 'تقسيط'] },
                        { name: 'source', label: 'المصدر', type: 'select', options: ['في المتجر', 'تطبيق توصيل', 'متجر إلكتروني'] }
                    ]
                }
            };

            const form = forms[type];
            if (!form) return;

            showModal(form.title, createForm(form.fields, type));
        }

        function createEditForm(fields, type, id) {
            return `
                <form id="editForm" class="space-y-4">
                    <input type="hidden" name="id" value="${id}">
                    <input type="hidden" name="type" value="${type}">
                    ${fields.map(field => {
                        if (field.type === 'select') {
                            return `
                                <div>
                                    <label class="block text-sm font-medium mb-2">${field.label}</label>
                                    <select name="${field.name}" class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none">
                                        ${field.options.map(option => `
                                            <option value="${option}" ${option === field.value ? 'selected' : ''}>${option}</option>
                                        `).join('')}
                                    </select>
                                </div>
                            `;
                        } else if (field.type === 'textarea') {
                            return `
                                <div>
                                    <label class="block text-sm font-medium mb-2">${field.label}</label>
                                    <textarea name="${field.name}" rows="3" class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none" placeholder="أدخل ${field.label}">${field.value || ''}</textarea>
                                </div>
                            `;
                        } else {
                            const extraAttrs = field.min ? `min="${field.min}"` : '';
                            const extraAttrs2 = field.max ? `max="${field.max}"` : '';
                            const extraAttrs3 = field.step ? `step="${field.step}"` : '';
                            return `
                                <div>
                                    <label class="block text-sm font-medium mb-2">${field.label}</label>
                                    <input type="${field.type}" name="${field.name}" value="${field.value || ''}"
                                           class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none"
                                           placeholder="أدخل ${field.label}" ${extraAttrs} ${extraAttrs2} ${extraAttrs3}>
                                </div>
                            `;
                        }
                    }).join('')}
                    <div class="flex gap-3 pt-4">
                        <button type="submit" class="btn flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                            💾 حفظ التعديلات
                        </button>
                        <button type="button" onclick="closeModal()" class="btn flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400">
                            ❌ إلغاء
                        </button>
                    </div>
                </form>
            `;
        }

        function createForm(fields, type) {
            return `
                <form id="itemForm" class="space-y-4">
                    ${fields.map(field => {
                        const requiredMark = field.required ? '<span class="text-red-500">*</span>' : '';
                        const requiredAttr = field.required ? 'required' : '';

                        if (field.type === 'select') {
                            return `
                                <div>
                                    <label class="block text-sm font-medium mb-2">${field.label} ${requiredMark}</label>
                                    <select name="${field.name}" class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none" ${requiredAttr}>
                                        <option value="">اختر ${field.label}</option>
                                        ${field.options.map(option => `<option value="${option}">${option}</option>`).join('')}
                                    </select>
                                </div>
                            `;
                        } else if (field.type === 'textarea') {
                            return `
                                <div>
                                    <label class="block text-sm font-medium mb-2">${field.label} ${requiredMark}</label>
                                    <textarea name="${field.name}" rows="3" class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none" placeholder="أدخل ${field.label}" ${requiredAttr}></textarea>
                                </div>
                            `;
                        } else {
                            return `
                                <div>
                                    <label class="block text-sm font-medium mb-2">${field.label} ${requiredMark}</label>
                                    <input type="${field.type}" name="${field.name}" class="form-input w-full px-3 py-2 border rounded-lg focus:border-blue-500 focus:outline-none" placeholder="أدخل ${field.label}" ${requiredAttr}>
                                </div>
                            `;
                        }
                    }).join('')}
                    <div class="flex gap-3 pt-4">
                        <button type="submit" class="btn flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                            💾 حفظ
                        </button>
                        <button type="button" onclick="closeModal()" class="btn flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400">
                            ❌ إلغاء
                        </button>
                    </div>
                </form>
            `;
        }

        function showEditModal(title, content, type, id) {
            const modal = `
                <div id="modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onclick="closeModal()">
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto" onclick="event.stopPropagation()">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold">${title}</h3>
                            <button onclick="closeModal()" class="icon-btn text-gray-500 hover:text-gray-700">❌</button>
                        </div>
                        ${content}
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modal);

            // إضافة مستمع للنموذج
            document.getElementById('editForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(e.target);
                const data = Object.fromEntries(formData);

                // تحديث البيانات في المصفوفة المناسبة
                updateItemData(type, id, data);

                showNotification('تم تحديث البيانات بنجاح! ✅', 'success');
                closeModal();

                // إعادة تحميل القسم لإظهار التحديثات
                forceReloadSection(getCurrentSection());
            });
        }

        function showModal(title, content) {
            const modal = `
                <div id="modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onclick="closeModal()">
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4" onclick="event.stopPropagation()">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold">${title}</h3>
                            <button onclick="closeModal()" class="icon-btn text-gray-500 hover:text-gray-700">❌</button>
                        </div>
                        ${content}
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modal);

            // إضافة مستمع للنموذج
            const form = document.getElementById('itemForm');
            if (!form) {
                console.error('لم يتم العثور على النموذج!');
                return;
            }

            form.addEventListener('submit', function(e) {
                e.preventDefault();
                console.log('تم إرسال النموذج');

                try {
                    const formData = new FormData(e.target);
                    const data = Object.fromEntries(formData);

                    console.log('بيانات النموذج الخام:', formData);
                    console.log('بيانات النموذج المحولة:', data);
                    console.log('نوع العنصر:', type);

                    // التحقق من وجود البيانات
                    if (Object.keys(data).length === 0) {
                        console.error('لا توجد بيانات في النموذج!');
                        showNotification('لا توجد بيانات للحفظ! ❌', 'error');
                        return;
                    }

                    // التحقق من صحة البيانات
                    if (!validateFormData(data, type)) {
                        console.log('فشل التحقق من البيانات');
                        return; // validateFormData تعرض الرسالة بنفسها
                    }

                    console.log('بدء عملية الحفظ...');

                    // إضافة العنصر الجديد للبيانات
                    const success = addNewItemToData(type, data);

                    if (success) {
                        console.log('تم الحفظ بنجاح');
                        showNotification('تم حفظ البيانات بنجاح! ✅', 'success');
                        closeModal();

                        // إعادة تحميل القسم فوراً
                        const currentSection = getCurrentSection();
                        console.log('إعادة تحميل القسم:', currentSection);

                        // إعادة تحميل بقوة
                        setTimeout(() => {
                            forceReloadSection(currentSection);
                        }, 100);
                    } else {
                        console.error('فشل في الحفظ');
                        showNotification('حدث خطأ أثناء حفظ البيانات! ❌', 'error');
                    }
                } catch (error) {
                    console.error('خطأ في معالجة النموذج:', error);
                    showNotification('حدث خطأ غير متوقع! ❌', 'error');
                }
            });
        }

        function closeModal() {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.remove();
            }
        }

        function showNotification(message, type = 'info') {
            const colors = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                warning: 'bg-yellow-500',
                info: 'bg-blue-500'
            };

            const notification = `
                <div id="notification" class="fixed top-4 right-4 ${colors[type]} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300">
                    ${message}
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', notification);

            const notif = document.getElementById('notification');
            setTimeout(() => notif.classList.remove('translate-x-full'), 100);
            setTimeout(() => {
                notif.classList.add('translate-x-full');
                setTimeout(() => notif.remove(), 300);
            }, 3000);
        }

        function viewItem(type, id) {
            const items = {
                purchase: mockData.purchases.find(p => p.id == id),
                payment: mockData.payments.find(p => p.id == id),
                supplier: mockData.suppliers.find(s => s.id == id),
                employee: mockData.employees.find(e => e.id == id),
                expense: mockData.expenses.find(e => e.id == id),
                sale: mockData.sales.find(s => s.id == id)
            };

            const item = items[type];
            if (!item) return;

            const content = `
                <div class="space-y-3">
                    ${Object.entries(item).map(([key, value]) => `
                        <div class="flex justify-between">
                            <span class="font-medium">${getFieldLabel(key)}:</span>
                            <span>${value}</span>
                        </div>
                    `).join('')}
                </div>
            `;

            showModal(`تفاصيل ${getTypeLabel(type)}`, content);
        }

        function editItem(type, id) {
            const items = {
                purchase: mockData.purchases.find(p => p.id == id),
                payment: mockData.payments.find(p => p.id == id),
                supplier: mockData.suppliers.find(s => s.id == id),
                employee: mockData.employees.find(e => e.id == id),
                expense: mockData.expenses.find(e => e.id == id),
                sale: mockData.sales.find(s => s.id == id)
            };

            const item = items[type];
            if (!item) {
                showNotification('لم يتم العثور على العنصر! ❌', 'error');
                return;
            }

            const forms = {
                purchase: {
                    title: 'تعديل طلب الشراء',
                    fields: [
                        { name: 'supplier', label: 'المورد', type: 'select', options: mockData.suppliers.map(s => s.name), value: item.supplier },
                        { name: 'invoice', label: 'رقم الفاتورة', type: 'text', value: item.invoice },
                        { name: 'amount', label: 'المبلغ', type: 'number', value: item.amount },
                        { name: 'items', label: 'عدد الأصناف', type: 'number', value: item.items },
                        { name: 'status', label: 'الحالة', type: 'select', options: ['في الانتظار', 'معتمد', 'مستلم', 'ملغي'], value: item.status }
                    ]
                },
                payment: {
                    title: 'تعديل الدفعة',
                    fields: [
                        { name: 'recipient', label: 'المستفيد', type: 'text', value: item.recipient },
                        { name: 'amount', label: 'المبلغ', type: 'number', value: item.amount },
                        { name: 'method', label: 'طريقة الدفع', type: 'select', options: ['نقدي', 'تحويل بنكي', 'شيك', 'بطاقة'], value: item.method },
                        { name: 'category', label: 'الفئة', type: 'select', options: ['موردين', 'مرافق', 'رواتب', 'تأمين', 'خدمات قانونية'], value: item.category },
                        { name: 'status', label: 'الحالة', type: 'select', options: ['مدفوع', 'معلق', 'متأخر', 'ملغي'], value: item.status },
                        { name: 'description', label: 'الوصف', type: 'textarea', value: item.description }
                    ]
                },
                supplier: {
                    title: 'تعديل بيانات المورد',
                    fields: [
                        { name: 'name', label: 'اسم الشركة', type: 'text', value: item.name },
                        { name: 'contact', label: 'الشخص المسؤول', type: 'text', value: item.contact },
                        { name: 'phone', label: 'رقم الهاتف', type: 'tel', value: item.phone },
                        { name: 'email', label: 'البريد الإلكتروني', type: 'email', value: item.email },
                        { name: 'city', label: 'المدينة', type: 'text', value: item.city },
                        { name: 'rating', label: 'التقييم', type: 'number', value: item.rating, min: 1, max: 5, step: 0.1 }
                    ]
                },
                employee: {
                    title: 'تعديل بيانات الموظف',
                    fields: [
                        { name: 'name', label: 'الاسم الكامل', type: 'text', value: item.name },
                        { name: 'position', label: 'المنصب', type: 'text', value: item.position },
                        { name: 'department', label: 'القسم', type: 'select', options: ['المبيعات', 'المالية', 'التقنية', 'الموارد البشرية'], value: item.department },
                        { name: 'phone', label: 'رقم الهاتف', type: 'tel', value: item.phone },
                        { name: 'email', label: 'البريد الإلكتروني', type: 'email', value: item.email },
                        { name: 'salary', label: 'الراتب', type: 'number', value: item.salary },
                        { name: 'status', label: 'الحالة', type: 'select', options: ['نشط', 'غير نشط', 'في إجازة'], value: item.status }
                    ]
                },
                expense: {
                    title: 'تعديل المصروف',
                    fields: [
                        { name: 'description', label: 'الوصف', type: 'text', value: item.description },
                        { name: 'amount', label: 'المبلغ', type: 'number', value: item.amount },
                        { name: 'category', label: 'الفئة', type: 'select', options: ['مرافق', 'مواصلات', 'مكتب', 'تقنية'], value: item.category },
                        { name: 'method', label: 'طريقة الدفع', type: 'select', options: ['نقدي', 'تحويل بنكي', 'شيك', 'بطاقة'], value: item.method },
                        { name: 'status', label: 'الحالة', type: 'select', options: ['معتمد', 'في الانتظار', 'مرفوض'], value: item.status }
                    ]
                },
                sale: {
                    title: 'تعديل المبيعة',
                    fields: [
                        { name: 'customer', label: 'اسم العميل', type: 'text', value: item.customer },
                        { name: 'phone', label: 'رقم الهاتف', type: 'tel', value: item.phone || '' },
                        { name: 'total', label: 'المبلغ الإجمالي', type: 'number', value: item.total },
                        { name: 'method', label: 'طريقة الدفع', type: 'select', options: ['نقدي', 'بطاقة', 'تحويل بنكي', 'تقسيط'], value: item.method },
                        { name: 'source', label: 'المصدر', type: 'select', options: ['في المتجر', 'تطبيق توصيل', 'متجر إلكتروني'], value: item.source },
                        { name: 'status', label: 'الحالة', type: 'select', options: ['مكتمل', 'معلق', 'ملغي', 'مسترد'], value: item.status }
                    ]
                }
            };

            const form = forms[type];
            if (!form) return;

            showEditModal(form.title, createEditForm(form.fields, type, id), type, id);
        }

        function deleteItem(type, id) {
            if (confirm('هل أنت متأكد من حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.')) {
                const dataArrays = {
                    purchase: mockData.purchases,
                    payment: mockData.payments,
                    supplier: mockData.suppliers,
                    employee: mockData.employees,
                    expense: mockData.expenses,
                    sale: mockData.sales
                };

                const array = dataArrays[type];
                if (!array) return;

                const itemIndex = array.findIndex(item => item.id == id);
                if (itemIndex !== -1) {
                    array.splice(itemIndex, 1);
                    showNotification('تم حذف العنصر بنجاح! 🗑️', 'success');

                    // إعادة تحميل القسم لإظهار التحديثات
                    forceReloadSection(getCurrentSection());
                } else {
                    showNotification('لم يتم العثور على العنصر! ❌', 'error');
                }
            }
        }

        function getFieldLabel(key) {
            const labels = {
                id: 'الرقم',
                name: 'الاسم',
                supplier: 'المورد',
                invoice: 'رقم الفاتورة',
                amount: 'المبلغ',
                date: 'التاريخ',
                status: 'الحالة',
                items: 'عدد الأصناف',
                contact: 'الشخص المسؤول',
                phone: 'الهاتف',
                email: 'البريد الإلكتروني',
                city: 'المدينة',
                position: 'المنصب',
                department: 'القسم',
                salary: 'الراتب',
                description: 'الوصف',
                category: 'الفئة',
                method: 'طريقة الدفع',
                customer: 'العميل',
                total: 'الإجمالي',
                source: 'المصدر'
            };
            return labels[key] || key;
        }

        function getTypeLabel(type) {
            const labels = {
                purchase: 'المشتريات',
                payment: 'المدفوعات',
                supplier: 'المورد',
                employee: 'الموظف',
                expense: 'المصروف',
                sale: 'المبيعة'
            };
            return labels[type] || type;
        }

        function confirmPayment(id) {
            if (confirm('هل أنت متأكد من تأكيد هذه الدفعة؟')) {
                // تحديث حالة الدفعة في البيانات الوهمية
                const payment = mockData.payments.find(p => p.id == id);
                if (payment) {
                    payment.status = 'مدفوع';
                    showNotification('تم تأكيد الدفعة بنجاح! ✅', 'success');
                    // إعادة تحميل الصفحة لإظهار التحديث
                    forceReloadSection('payments');
                }
            }
        }

        function updateItemData(type, id, newData) {
            const dataArrays = {
                purchase: mockData.purchases,
                payment: mockData.payments,
                supplier: mockData.suppliers,
                employee: mockData.employees,
                expense: mockData.expenses,
                sale: mockData.sales
            };

            const array = dataArrays[type];
            if (!array) return false;

            const itemIndex = array.findIndex(item => item.id == id);
            if (itemIndex === -1) return false;

            // تحديث البيانات (تحويل الأرقام من النص إلى رقم)
            Object.keys(newData).forEach(key => {
                if (key !== 'id' && key !== 'type') {
                    let value = newData[key];

                    // تحويل الحقول الرقمية
                    if (['amount', 'items', 'salary', 'total', 'rating'].includes(key)) {
                        value = parseFloat(value) || 0;
                    }

                    array[itemIndex][key] = value;
                }
            });

            return true;
        }

        function validateFormData(data, type) {
            console.log('التحقق من البيانات:', data, 'النوع:', type);

            const requiredFields = {
                purchase: ['supplier', 'invoice', 'amount'],
                payment: ['recipient', 'amount', 'method', 'category'],
                supplier: ['name', 'contact', 'phone', 'email'],
                employee: ['name', 'position', 'department', 'salary'],
                expense: ['description', 'amount', 'category'],
                sale: ['customer', 'total', 'method']
            };

            const required = requiredFields[type] || [];
            console.log('الحقول المطلوبة:', required);

            for (let field of required) {
                const value = data[field];
                console.log(`فحص الحقل ${field}:`, value);

                if (!value || (typeof value === 'string' && value.trim() === '')) {
                    console.log(`حقل مطلوب فارغ: ${field}`);
                    showNotification(`الحقل "${getFieldLabel(field)}" مطلوب! ❌`, 'error');
                    return false;
                }
            }

            // التحقق من الحقول الرقمية
            const numericFields = ['amount', 'salary', 'total'];
            for (let field of numericFields) {
                if (data[field] && isNaN(parseFloat(data[field]))) {
                    console.log(`حقل رقمي غير صحيح: ${field}`);
                    showNotification(`الحقل "${getFieldLabel(field)}" يجب أن يكون رقماً! ❌`, 'error');
                    return false;
                }
            }

            console.log('تم التحقق من البيانات بنجاح');
            return true;
        }

        function addNewItemToData(type, data) {
            console.log('بدء إضافة عنصر جديد:', type, data);

            const dataArrays = {
                purchase: mockData.purchases,
                payment: mockData.payments,
                supplier: mockData.suppliers,
                employee: mockData.employees,
                expense: mockData.expenses,
                sale: mockData.sales
            };

            const array = dataArrays[type];
            if (!array) {
                console.error('نوع البيانات غير صحيح:', type);
                return false;
            }

            console.log('المصفوفة قبل الإضافة:', array.length, 'عنصر');

            // إنشاء ID جديد
            const newId = array.length > 0 ? Math.max(...array.map(item => item.id)) + 1 : 1;
            console.log('ID الجديد:', newId);

            // إنشاء العنصر الجديد حسب النوع
            let newItem = { id: newId };

            // تحويل البيانات وإضافة الحقول المطلوبة
            Object.keys(data).forEach(key => {
                let value = data[key];

                // تحويل الحقول الرقمية
                if (['amount', 'items', 'salary', 'total', 'rating'].includes(key)) {
                    value = parseFloat(value) || 0;
                }

                newItem[key] = value;
            });

            // إضافة حقول افتراضية حسب النوع
            const currentDate = new Date().toISOString().split('T')[0];

            switch(type) {
                case 'purchase':
                    newItem.date = currentDate;
                    newItem.status = newItem.status || 'في الانتظار';
                    if (!newItem.items) newItem.items = 1;
                    break;
                case 'payment':
                    newItem.date = currentDate;
                    newItem.dueDate = currentDate;
                    newItem.status = newItem.status || 'معلق';
                    if (!newItem.description) newItem.description = 'دفعة جديدة';
                    break;
                case 'supplier':
                    newItem.totalPurchases = 0;
                    newItem.balance = 0;
                    newItem.rating = parseFloat(newItem.rating) || 5.0;
                    newItem.city = newItem.city || 'غير محدد';
                    console.log('تم إنشاء مورد جديد:', newItem);
                    break;
                case 'employee':
                    newItem.employeeId = `EMP-${String(newId).padStart(3, '0')}`;
                    newItem.status = newItem.status || 'نشط';
                    newItem.phone = newItem.phone || 'غير محدد';
                    newItem.email = newItem.email || 'غير محدد';
                    newItem.salary = parseFloat(newItem.salary) || 0;
                    console.log('تم إنشاء موظف جديد:', newItem);
                    break;
                case 'expense':
                    newItem.date = currentDate;
                    newItem.status = newItem.status || 'في الانتظار';
                    newItem.addedBy = 'المستخدم الحالي';
                    if (!newItem.method) newItem.method = 'نقدي';
                    break;
                case 'sale':
                    newItem.invoice = `INV-2024-${String(newId).padStart(3, '0')}`;
                    newItem.date = currentDate;
                    newItem.status = newItem.status || 'مكتمل';
                    newItem.salesperson = 'المستخدم الحالي';
                    if (!newItem.source) newItem.source = 'في المتجر';
                    break;
            }

            console.log('العنصر الجديد:', newItem);

            // إضافة العنصر للمصفوفة
            array.push(newItem);

            console.log('المصفوفة بعد الإضافة:', array.length, 'عنصر');
            console.log('آخر عنصر مضاف:', array[array.length - 1]);

            return true;
        }

        function getCurrentSection() {
            console.log('القسم الحالي المحفوظ:', currentActiveSection);
            return currentActiveSection;
        }

        function forceReloadSection(section) {
            console.log('إعادة تحميل بقوة للقسم:', section);

            // مسح المحتوى الحالي
            const content = document.getElementById('main-content');
            content.innerHTML = '<div class="flex items-center justify-center h-64"><p class="text-lg">جاري التحميل...</p></div>';

            // تأخير قصير ثم إعادة التحميل
            setTimeout(() => {
                showSection(section);
                console.log('تم إعادة تحميل القسم بنجاح');
            }, 200);
        }

        function searchItems(searchTerm, section) {
            if (!searchTerm.trim()) {
                showSection(section);
                return;
            }

            const searchFields = {
                purchases: ['supplier', 'invoice', 'description'],
                payments: ['recipient', 'category', 'description'],
                suppliers: ['name', 'contact', 'email', 'city'],
                employees: ['name', 'position', 'department', 'email'],
                expenses: ['description', 'category', 'addedBy'],
                sales: ['customer', 'invoice', 'phone']
            };

            const dataArrays = {
                purchases: mockData.purchases,
                payments: mockData.payments,
                suppliers: mockData.suppliers,
                employees: mockData.employees,
                expenses: mockData.expenses,
                sales: mockData.sales
            };

            const fields = searchFields[section];
            const data = dataArrays[section];

            if (!fields || !data) {
                showNotification('قسم غير صحيح للبحث! ❌', 'error');
                return;
            }

            const filteredData = data.filter(item => {
                return fields.some(field => {
                    const value = item[field];
                    return value && value.toString().toLowerCase().includes(searchTerm.toLowerCase());
                });
            });

            if (filteredData.length === 0) {
                showNotification(`لم يتم العثور على نتائج للبحث: "${searchTerm}" 🔍`, 'warning');
            } else {
                showNotification(`تم العثور على ${filteredData.length} نتيجة للبحث 🔍`, 'success');
            }

            // تحديث البيانات مؤقتاً لإظهار النتائج المفلترة
            const originalData = { ...mockData };
            mockData[section] = filteredData;
            showSection(section);

            // إعادة البيانات الأصلية بعد 10 ثوانٍ
            setTimeout(() => {
                Object.assign(mockData, originalData);
            }, 10000);
        }

        // إضافة مستمعات للبحث في جميع حقول البحث
        function addSearchListeners() {
            document.addEventListener('input', function(e) {
                if (e.target.placeholder && e.target.placeholder.includes('البحث')) {
                    const searchTerm = e.target.value;
                    const currentSection = getCurrentSection();

                    // تأخير البحث لتحسين الأداء
                    clearTimeout(window.searchTimeout);
                    window.searchTimeout = setTimeout(() => {
                        if (searchTerm.length >= 2 || searchTerm.length === 0) {
                            searchItems(searchTerm, currentSection);
                        }
                    }, 500);
                }
            });
        }

        // تحديث التاريخ والوقت
        function updateDateTime() {
            const now = new Date();

            // تحديث التاريخ الميلادي
            const dateOptions = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            const currentDate = now.toLocaleDateString('ar-SA', dateOptions);

            // تحديث التاريخ الهجري
            let hijriDate;
            try {
                hijriDate = now.toLocaleDateString('ar-SA-u-ca-islamic', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            } catch (e) {
                // في حالة عدم دعم التقويم الهجري، استخدم تاريخ تقريبي
                hijriDate = 'غير متوفر';
            }

            // تحديث الوقت
            const timeOptions = {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            };
            const currentTime = now.toLocaleTimeString('ar-SA', timeOptions);

            // تحديث العناصر في الصفحة
            const dateElement = document.getElementById('current-date');
            const hijriElement = document.getElementById('hijri-date');
            const timeElement = document.getElementById('last-update');

            if (dateElement) {
                dateElement.textContent = `اليوم: ${currentDate}`;
            }

            if (hijriElement) {
                hijriElement.textContent = `التاريخ الهجري: ${hijriDate}`;
            }

            if (timeElement) {
                timeElement.textContent = `آخر تحديث: ${currentTime}`;
            }

            // تحديث الساعة في لوحة التحكم إذا كانت موجودة
            const dashboardClock = document.getElementById('dashboard-clock');
            const dashboardHijri = document.getElementById('dashboard-hijri');

            if (dashboardClock) {
                dashboardClock.textContent = currentTime;
            }

            if (dashboardHijri) {
                dashboardHijri.textContent = hijriDate;
            }
        }

        // تحديث التاريخ والوقت كل ثانية
        function startDateTimeUpdater() {
            updateDateTime(); // تحديث فوري
            setInterval(updateDateTime, 1000); // تحديث كل ثانية
        }

        // دالة اختبار لإضافة بيانات تجريبية
        function testAddData() {
            console.log('اختبار إضافة البيانات...');

            // اختبار إضافة مورد
            const testSupplier = {
                name: 'شركة اختبار',
                contact: 'أحمد محمد',
                phone: '+966501234567',
                email: '<EMAIL>',
                city: 'الرياض'
            };

            console.log('إضافة مورد تجريبي:', testSupplier);
            const supplierSuccess = addNewItemToData('supplier', testSupplier);
            console.log('نتيجة إضافة المورد:', supplierSuccess);

            // اختبار إضافة موظف
            const testEmployee = {
                name: 'موظف تجريبي',
                position: 'مطور',
                department: 'التقنية',
                phone: '+966509876543',
                email: '<EMAIL>',
                salary: '8000'
            };

            console.log('إضافة موظف تجريبي:', testEmployee);
            const employeeSuccess = addNewItemToData('employee', testEmployee);
            console.log('نتيجة إضافة الموظف:', employeeSuccess);

            if (supplierSuccess && employeeSuccess) {
                console.log('تم اختبار الإضافة بنجاح!');
                showNotification('تم إضافة البيانات التجريبية بنجاح! 🧪', 'success');
            }
        }

        // تحميل لوحة التحكم عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            showSection('dashboard');
            // تفعيل العنصر الأول في الشريط الجانبي
            document.querySelector('.sidebar-item').classList.add('active');
            // إضافة مستمعات البحث
            addSearchListeners();
            // بدء تحديث التاريخ والوقت
            startDateTimeUpdater();

            // إضافة زر اختبار في وحدة التحكم
            console.log('لاختبار إضافة البيانات، اكتب: testAddData()');
        });
    </script>
</body>
</html>
