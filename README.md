# 📊 نظام المحاسبة الشامل

تطبيق محاسبة متكامل وقابل للتخصيص مصمم خصيصاً للشركات والمؤسسات في المنطقة العربية.

## ✨ المميزات الرئيسية

### 🏠 لوحة التحكم الذكية
- **مؤشرات الأداء الرئيسية (KPIs)** مع رسوم بيانية تفاعلية
- **خلاصة الأنشطة** في الوقت الفعلي
- **الإجراءات السريعة** للعمليات الشائعة
- **رسوم بيانية متقدمة** للإيرادات والمصروفات

### 🛒 إدارة المشتريات
- تتبع طلبات الشراء والموافقات
- إدارة الفواتير الواردة
- متابعة حالة الطلبات (معلق، معتمد، مستلم)
- تقارير شاملة للمشتريات

### 💳 إدارة المدفوعات
- تسجيل جميع أنواع المدفوعات
- تتبع المواعيد النهائية والمدفوعات المتأخرة
- دعم طرق دفع متعددة (نقدي، بنكي، شيك، بطاقة)
- إشعارات للمدفوعات المستحقة

### 🏢 إدارة الموردين
- قاعدة بيانات شاملة للموردين
- تتبع تاريخ المعاملات والأرصدة
- نظام تقييم الموردين
- إدارة معلومات الاتصال والعقود

### 👥 إدارة الموظفين
- ملفات شخصية كاملة للموظفين
- إدارة الرواتب والمكافآت
- تتبع الحضور والإجازات
- تقارير الموارد البشرية

### 💰 المصروفات اليومية
- تصنيف المصروفات حسب الفئات
- دعم المصروفات الدورية والمتكررة
- نظام الموافقات والاعتمادات
- تتبع الإيصالات والمستندات

### 📈 إدخال المبيعات
- دعم مصادر متعددة (متجر، تطبيقات توصيل، أونلاين)
- إدارة الفواتير والعملاء
- تتبع المخزون والكميات
- تقارير المبيعات التفصيلية

## 🎨 المظهر والتخصيص

### 🌙 دعم الوضع الليلي/النهاري
- تبديل سلس بين الأوضاع
- حفظ تفضيلات المستخدم
- تصميم متجاوب مع جميع الأجهزة

### 🌍 دعم متعدد اللغات
- **العربية** (افتراضي)
- **الإنجليزية**
- **الفرنسية**
- إمكانية إضافة لغات جديدة بسهولة

### 💱 دعم العملات المتعددة
- **الريال السعودي** (افتراضي)
- **الدولار الأمريكي**
- **اليورو**
- **الدرهم الإماراتي**
- تحويل تلقائي بين العملات

## 🛠️ التقنيات المستخدمة

### Frontend
- **Next.js 14** - إطار العمل الرئيسي
- **React 18** - مكتبة واجهة المستخدم
- **TypeScript** - للأمان والموثوقية
- **Tailwind CSS** - للتصميم المتجاوب
- **Recharts** - للرسوم البيانية التفاعلية

### UI Components
- **shadcn/ui** - مكونات واجهة المستخدم
- **Lucide React** - الأيقونات
- **next-themes** - إدارة الثيمات
- **React Hook Form** - إدارة النماذج

### State Management
- **Zustand** - إدارة الحالة الخفيفة
- **React Hot Toast** - الإشعارات

## 🚀 التثبيت والتشغيل

### المتطلبات
- Node.js 18+ 
- npm أو yarn أو pnpm

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/accounting-app.git
cd accounting-app
```

2. **تثبيت المكتبات**
```bash
npm install
# أو
yarn install
# أو
pnpm install
```

3. **تشغيل التطبيق**
```bash
npm run dev
# أو
yarn dev
# أو
pnpm dev
```

4. **فتح التطبيق**
افتح [http://localhost:3000](http://localhost:3000) في المتصفح

## 📁 هيكل المشروع

```
src/
├── app/                    # صفحات Next.js
│   ├── globals.css        # الأنماط العامة
│   ├── layout.tsx         # التخطيط الرئيسي
│   ├── page.tsx          # الصفحة الرئيسية
│   ├── purchases/        # صفحة المشتريات
│   ├── payments/         # صفحة المدفوعات
│   ├── suppliers/        # صفحة الموردين
│   ├── employees/        # صفحة الموظفين
│   ├── expenses/         # صفحة المصروفات
│   ├── sales/           # صفحة المبيعات
│   └── settings/        # صفحة الإعدادات
├── components/           # المكونات القابلة لإعادة الاستخدام
│   ├── ui/              # مكونات واجهة المستخدم الأساسية
│   ├── layout/          # مكونات التخطيط
│   ├── dashboard/       # مكونات لوحة التحكم
│   ├── modules/         # وحدات التطبيق
│   └── settings/        # مكونات الإعدادات
└── lib/                 # المكتبات والأدوات المساعدة
    └── utils.ts         # دوال مساعدة
```

## 🎯 الاستخدام

### لوحة التحكم
- عرض المؤشرات المالية الرئيسية
- متابعة الأنشطة الحديثة
- الوصول السريع للعمليات الشائعة

### إدارة البيانات
- إضافة وتعديل وحذف السجلات
- البحث والتصفية المتقدمة
- تصدير البيانات بصيغ مختلفة

### التقارير
- تقارير مالية شاملة
- رسوم بيانية تفاعلية
- إمكانية الطباعة والتصدير

## 🔧 التخصيص

### إضافة لغة جديدة
1. إضافة ملف الترجمة في `src/locales/`
2. تحديث قائمة اللغات في `src/components/settings/SettingsPanel.tsx`
3. إضافة دعم اللغة في `next.config.js`

### إضافة عملة جديدة
1. تحديث قائمة العملات في `src/components/settings/SettingsPanel.tsx`
2. إضافة منطق التحويل في `src/lib/utils.ts`

### تخصيص الألوان
- تعديل متغيرات الألوان في `src/app/globals.css`
- استخدام نظام الألوان المتوافق مع Tailwind CSS

## 📊 قاعدة البيانات (اختيارية)

يمكن ربط التطبيق بقاعدة بيانات حقيقية:

### Prisma + PostgreSQL
```bash
npm install prisma @prisma/client
npx prisma init
```

### Supabase
```bash
npm install @supabase/supabase-js
```

## 🔐 الأمان

- تشفير البيانات الحساسة
- نظام صلاحيات متدرج
- حماية من هجمات CSRF و XSS
- نسخ احتياطية دورية

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إضافة التغييرات والاختبارات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم والتواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: https://accounting-app.com
- **التوثيق**: https://docs.accounting-app.com

---

**تم تطويره بـ ❤️ للمجتمع العربي**
