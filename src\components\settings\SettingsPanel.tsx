"use client"

import React, { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useTheme } from 'next-themes'
import {
  Settings,
  Globe,
  Palette,
  DollarSign,
  Bell,
  Shield,
  Database,
  Download,
  Upload,
  Save,
  Moon,
  Sun,
} from 'lucide-react'

interface SettingsSection {
  id: string
  title: string
  icon: React.ReactNode
  description: string
}

const settingsSections: SettingsSection[] = [
  {
    id: 'general',
    title: 'الإعدادات العامة',
    icon: <Settings className="h-5 w-5" />,
    description: 'إعدادات التطبيق الأساسية',
  },
  {
    id: 'language',
    title: 'اللغة والمنطقة',
    icon: <Globe className="h-5 w-5" />,
    description: 'تغيير اللغة والمنطقة الزمنية',
  },
  {
    id: 'appearance',
    title: 'المظهر',
    icon: <Palette className="h-5 w-5" />,
    description: 'تخصيص مظهر التطبيق',
  },
  {
    id: 'currency',
    title: 'العملة والضرائب',
    icon: <DollarSign className="h-5 w-5" />,
    description: 'إعدادات العملة ونسب الضرائب',
  },
  {
    id: 'notifications',
    title: 'الإشعارات',
    icon: <Bell className="h-5 w-5" />,
    description: 'إدارة الإشعارات والتنبيهات',
  },
  {
    id: 'security',
    title: 'الأمان والخصوصية',
    icon: <Shield className="h-5 w-5" />,
    description: 'إعدادات الأمان وحماية البيانات',
  },
  {
    id: 'backup',
    title: 'النسخ الاحتياطي',
    icon: <Database className="h-5 w-5" />,
    description: 'إدارة النسخ الاحتياطية للبيانات',
  },
]

const languages = [
  { code: 'ar', name: 'العربية', flag: '🇸🇦' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
]

const currencies = [
  { code: 'SAR', name: 'ريال سعودي', symbol: 'ر.س' },
  { code: 'USD', name: 'دولار أمريكي', symbol: '$' },
  { code: 'EUR', name: 'يورو', symbol: '€' },
  { code: 'AED', name: 'درهم إماراتي', symbol: 'د.إ' },
]

export function SettingsPanel() {
  const { theme, setTheme } = useTheme()
  const [activeSection, setActiveSection] = useState('general')
  const [settings, setSettings] = useState({
    language: 'ar',
    currency: 'SAR',
    taxRate: 15,
    companyName: 'شركة المحاسبة المحدودة',
    companyAddress: 'الرياض، المملكة العربية السعودية',
    companyPhone: '+966501234567',
    companyEmail: '<EMAIL>',
    notifications: {
      email: true,
      push: true,
      sms: false,
      lowStock: true,
      overduePayments: true,
    },
    security: {
      twoFactor: false,
      sessionTimeout: 30,
      passwordExpiry: 90,
    },
  })

  const updateSetting = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }))
  }

  const updateNestedSetting = (section: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: { ...prev[section as keyof typeof prev], [key]: value }
    }))
  }

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium mb-2">اسم الشركة</label>
        <input
          type="text"
          value={settings.companyName}
          onChange={(e) => updateSetting('companyName', e.target.value)}
          className="w-full h-10 rounded-lg border border-input bg-background px-3 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
        />
      </div>
      <div>
        <label className="block text-sm font-medium mb-2">عنوان الشركة</label>
        <textarea
          value={settings.companyAddress}
          onChange={(e) => updateSetting('companyAddress', e.target.value)}
          rows={3}
          className="w-full rounded-lg border border-input bg-background px-3 py-2 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
        />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-2">رقم الهاتف</label>
          <input
            type="tel"
            value={settings.companyPhone}
            onChange={(e) => updateSetting('companyPhone', e.target.value)}
            className="w-full h-10 rounded-lg border border-input bg-background px-3 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">البريد الإلكتروني</label>
          <input
            type="email"
            value={settings.companyEmail}
            onChange={(e) => updateSetting('companyEmail', e.target.value)}
            className="w-full h-10 rounded-lg border border-input bg-background px-3 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
          />
        </div>
      </div>
    </div>
  )

  const renderLanguageSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium mb-2">اللغة</label>
        <select
          value={settings.language}
          onChange={(e) => updateSetting('language', e.target.value)}
          className="w-full h-10 rounded-lg border border-input bg-background px-3 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
        >
          {languages.map(lang => (
            <option key={lang.code} value={lang.code}>
              {lang.flag} {lang.name}
            </option>
          ))}
        </select>
      </div>
    </div>
  )

  const renderAppearanceSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium mb-2">المظهر</label>
        <div className="grid grid-cols-3 gap-3">
          <button
            onClick={() => setTheme('light')}
            className={`flex items-center justify-center gap-2 p-3 rounded-lg border ${
              theme === 'light' ? 'border-primary bg-primary/10' : 'border-input'
            }`}
          >
            <Sun className="h-4 w-4" />
            فاتح
          </button>
          <button
            onClick={() => setTheme('dark')}
            className={`flex items-center justify-center gap-2 p-3 rounded-lg border ${
              theme === 'dark' ? 'border-primary bg-primary/10' : 'border-input'
            }`}
          >
            <Moon className="h-4 w-4" />
            داكن
          </button>
          <button
            onClick={() => setTheme('system')}
            className={`flex items-center justify-center gap-2 p-3 rounded-lg border ${
              theme === 'system' ? 'border-primary bg-primary/10' : 'border-input'
            }`}
          >
            <Settings className="h-4 w-4" />
            تلقائي
          </button>
        </div>
      </div>
    </div>
  )

  const renderCurrencySettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium mb-2">العملة الأساسية</label>
        <select
          value={settings.currency}
          onChange={(e) => updateSetting('currency', e.target.value)}
          className="w-full h-10 rounded-lg border border-input bg-background px-3 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
        >
          {currencies.map(currency => (
            <option key={currency.code} value={currency.code}>
              {currency.symbol} {currency.name}
            </option>
          ))}
        </select>
      </div>
      <div>
        <label className="block text-sm font-medium mb-2">نسبة الضريبة (%)</label>
        <input
          type="number"
          value={settings.taxRate}
          onChange={(e) => updateSetting('taxRate', parseFloat(e.target.value))}
          min="0"
          max="100"
          step="0.1"
          className="w-full h-10 rounded-lg border border-input bg-background px-3 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
        />
      </div>
    </div>
  )

  const renderBackupSettings = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between p-4 border rounded-lg">
        <div>
          <h3 className="font-medium">تصدير البيانات</h3>
          <p className="text-sm text-muted-foreground">تحميل نسخة من جميع البيانات</p>
        </div>
        <Button variant="outline" className="gap-2">
          <Download className="h-4 w-4" />
          تصدير
        </Button>
      </div>
      <div className="flex items-center justify-between p-4 border rounded-lg">
        <div>
          <h3 className="font-medium">استيراد البيانات</h3>
          <p className="text-sm text-muted-foreground">استعادة البيانات من نسخة احتياطية</p>
        </div>
        <Button variant="outline" className="gap-2">
          <Upload className="h-4 w-4" />
          استيراد
        </Button>
      </div>
    </div>
  )

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'general':
        return renderGeneralSettings()
      case 'language':
        return renderLanguageSettings()
      case 'appearance':
        return renderAppearanceSettings()
      case 'currency':
        return renderCurrencySettings()
      case 'backup':
        return renderBackupSettings()
      default:
        return <div>قسم الإعدادات غير متوفر حالياً</div>
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">الإعدادات</h1>
        <p className="text-muted-foreground">
          إدارة إعدادات التطبيق والتفضيلات الشخصية
        </p>
      </div>

      <div className="grid gap-6 lg:grid-cols-4">
        {/* قائمة الإعدادات */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>أقسام الإعدادات</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {settingsSections.map((section) => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`w-full flex items-center gap-3 p-3 rounded-lg text-right transition-colors ${
                  activeSection === section.id
                    ? 'bg-primary text-primary-foreground'
                    : 'hover:bg-muted'
                }`}
              >
                {section.icon}
                <div>
                  <p className="font-medium">{section.title}</p>
                  <p className="text-xs opacity-80">{section.description}</p>
                </div>
              </button>
            ))}
          </CardContent>
        </Card>

        {/* محتوى الإعدادات */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle>
              {settingsSections.find(s => s.id === activeSection)?.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {renderSectionContent()}
            <div className="flex justify-end mt-6">
              <Button className="gap-2">
                <Save className="h-4 w-4" />
                حفظ التغييرات
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
