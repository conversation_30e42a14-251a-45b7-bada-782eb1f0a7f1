"use client"

import React, { useState } from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { formatCurrency, formatDate } from '@/lib/utils'
import {
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Package,
  Calendar,
  DollarSign,
} from 'lucide-react'

interface Purchase {
  id: string
  supplierName: string
  invoiceNumber: string
  date: Date
  amount: number
  status: 'pending' | 'approved' | 'received' | 'cancelled'
  items: number
  description: string
}

const mockPurchases: Purchase[] = [
  {
    id: '1',
    supplierName: 'شركة التوريد المحدودة',
    invoiceNumber: 'INV-2024-001',
    date: new Date('2024-01-15'),
    amount: 15000,
    status: 'received',
    items: 25,
    description: 'مواد خام للإنتاج',
  },
  {
    id: '2',
    supplierName: 'مؤسسة الجودة التجارية',
    invoiceNumber: 'INV-2024-002',
    date: new Date('2024-01-14'),
    amount: 8500,
    status: 'approved',
    items: 12,
    description: 'معدات مكتبية',
  },
  {
    id: '3',
    supplierName: 'شركة التقنية المتقدمة',
    invoiceNumber: 'INV-2024-003',
    date: new Date('2024-01-13'),
    amount: 22000,
    status: 'pending',
    items: 8,
    description: 'أجهزة كمبيوتر',
  },
]

function getStatusColor(status: Purchase['status']) {
  switch (status) {
    case 'received':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    case 'approved':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
    case 'cancelled':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

function getStatusText(status: Purchase['status']) {
  switch (status) {
    case 'received':
      return 'مستلم'
    case 'approved':
      return 'معتمد'
    case 'pending':
      return 'في الانتظار'
    case 'cancelled':
      return 'ملغي'
    default:
      return 'غير محدد'
  }
}

export function PurchasesModule() {
  const [purchases] = useState<Purchase[]>(mockPurchases)
  const [searchTerm, setSearchTerm] = useState('')

  const filteredPurchases = purchases.filter(purchase =>
    purchase.supplierName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    purchase.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const totalAmount = purchases.reduce((sum, purchase) => sum + purchase.amount, 0)
  const pendingCount = purchases.filter(p => p.status === 'pending').length
  const receivedCount = purchases.filter(p => p.status === 'received').length

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">إدارة المشتريات</h1>
          <p className="text-muted-foreground">
            تتبع وإدارة جميع عمليات الشراء والموردين
          </p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          طلب شراء جديد
        </Button>
      </div>

      {/* بطاقات الإحصائيات */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المشتريات</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalAmount)}</div>
            <p className="text-xs text-muted-foreground">هذا الشهر</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">طلبات معلقة</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingCount}</div>
            <p className="text-xs text-muted-foreground">تحتاج موافقة</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">طلبات مستلمة</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{receivedCount}</div>
            <p className="text-xs text-muted-foreground">تم الاستلام</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">عدد الموردين</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">مورد نشط</p>
          </CardContent>
        </Card>
      </div>

      {/* أدوات البحث والتصفية */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>قائمة المشتريات</CardTitle>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="البحث في المشتريات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="h-10 w-80 rounded-lg border border-input bg-background pr-10 pl-4 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                />
              </div>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-right p-4 font-medium">رقم الفاتورة</th>
                  <th className="text-right p-4 font-medium">المورد</th>
                  <th className="text-right p-4 font-medium">التاريخ</th>
                  <th className="text-right p-4 font-medium">المبلغ</th>
                  <th className="text-right p-4 font-medium">عدد الأصناف</th>
                  <th className="text-right p-4 font-medium">الحالة</th>
                  <th className="text-right p-4 font-medium">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredPurchases.map((purchase) => (
                  <tr key={purchase.id} className="border-b hover:bg-muted/50">
                    <td className="p-4 font-medium">{purchase.invoiceNumber}</td>
                    <td className="p-4">{purchase.supplierName}</td>
                    <td className="p-4">{formatDate(purchase.date)}</td>
                    <td className="p-4 font-medium">{formatCurrency(purchase.amount)}</td>
                    <td className="p-4">{purchase.items}</td>
                    <td className="p-4">
                      <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(purchase.status)}`}>
                        {getStatusText(purchase.status)}
                      </span>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
