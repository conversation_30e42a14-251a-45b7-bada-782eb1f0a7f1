"use client"

import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { formatCurrency, formatDate } from '@/lib/utils'
import {
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  TrendingUp,
  Calendar,
  ShoppingCart,
  Users,
  Truck,
  Store,
} from 'lucide-react'

interface Sale {
  id: string
  invoiceNumber: string
  customerName: string
  customerPhone?: string
  date: Date
  items: SaleItem[]
  subtotal: number
  tax: number
  discount: number
  total: number
  paymentMethod: 'cash' | 'card' | 'bank' | 'installment'
  status: 'completed' | 'pending' | 'cancelled' | 'refunded'
  source: 'in_store' | 'delivery_app' | 'online' | 'phone'
  deliveryApp?: string
  notes?: string
  salesPerson: string
}

interface SaleItem {
  id: string
  name: string
  quantity: number
  unitPrice: number
  total: number
}

const mockSales: Sale[] = [
  {
    id: '1',
    invoiceNumber: 'INV-2024-001',
    customerName: 'أحمد محمد',
    customerPhone: '+************',
    date: new Date('2024-01-15'),
    items: [
      { id: '1', name: 'منتج A', quantity: 2, unitPrice: 100, total: 200 },
      { id: '2', name: 'منتج B', quantity: 1, unitPrice: 150, total: 150 },
    ],
    subtotal: 350,
    tax: 52.5,
    discount: 0,
    total: 402.5,
    paymentMethod: 'cash',
    status: 'completed',
    source: 'in_store',
    salesPerson: 'فاطمة أحمد',
  },
  {
    id: '2',
    invoiceNumber: 'INV-2024-002',
    customerName: 'سارة علي',
    date: new Date('2024-01-14'),
    items: [
      { id: '3', name: 'منتج C', quantity: 3, unitPrice: 80, total: 240 },
    ],
    subtotal: 240,
    tax: 36,
    discount: 20,
    total: 256,
    paymentMethod: 'card',
    status: 'completed',
    source: 'delivery_app',
    deliveryApp: 'طلبات',
    salesPerson: 'محمد علي',
  },
  {
    id: '3',
    invoiceNumber: 'INV-2024-003',
    customerName: 'خالد أحمد',
    customerPhone: '+966509876543',
    date: new Date('2024-01-13'),
    items: [
      { id: '4', name: 'منتج D', quantity: 1, unitPrice: 500, total: 500 },
      { id: '5', name: 'منتج E', quantity: 2, unitPrice: 75, total: 150 },
    ],
    subtotal: 650,
    tax: 97.5,
    discount: 50,
    total: 697.5,
    paymentMethod: 'installment',
    status: 'pending',
    source: 'online',
    salesPerson: 'أحمد محمد',
  },
]

function getStatusColor(status: Sale['status']) {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
    case 'cancelled':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
    case 'refunded':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

function getStatusText(status: Sale['status']) {
  switch (status) {
    case 'completed':
      return 'مكتمل'
    case 'pending':
      return 'معلق'
    case 'cancelled':
      return 'ملغي'
    case 'refunded':
      return 'مسترد'
    default:
      return 'غير محدد'
  }
}

function getSourceIcon(source: Sale['source']) {
  switch (source) {
    case 'in_store':
      return <Store className="h-4 w-4" />
    case 'delivery_app':
      return <Truck className="h-4 w-4" />
    case 'online':
      return <ShoppingCart className="h-4 w-4" />
    case 'phone':
      return <Users className="h-4 w-4" />
    default:
      return <Store className="h-4 w-4" />
  }
}

function getSourceText(source: Sale['source']) {
  switch (source) {
    case 'in_store':
      return 'في المتجر'
    case 'delivery_app':
      return 'تطبيق توصيل'
    case 'online':
      return 'متجر إلكتروني'
    case 'phone':
      return 'هاتف'
    default:
      return 'غير محدد'
  }
}

function getPaymentMethodText(method: Sale['paymentMethod']) {
  switch (method) {
    case 'cash':
      return 'نقدي'
    case 'card':
      return 'بطاقة'
    case 'bank':
      return 'تحويل بنكي'
    case 'installment':
      return 'تقسيط'
    default:
      return 'غير محدد'
  }
}

export function SalesModule() {
  const [sales] = useState<Sale[]>(mockSales)
  const [searchTerm, setSearchTerm] = useState('')

  const filteredSales = sales.filter(sale =>
    sale.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    sale.customerName.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const totalSales = sales.reduce((sum, sale) => sum + sale.total, 0)
  const completedSales = sales.filter(s => s.status === 'completed')
  const totalCompleted = completedSales.reduce((sum, sale) => sum + sale.total, 0)
  const pendingCount = sales.filter(s => s.status === 'pending').length
  const averageSale = completedSales.length > 0 ? totalCompleted / completedSales.length : 0

  // تجميع المبيعات حسب المصدر
  const salesBySource = [
    { source: 'in_store', count: sales.filter(s => s.source === 'in_store').length, total: sales.filter(s => s.source === 'in_store').reduce((sum, s) => sum + s.total, 0) },
    { source: 'delivery_app', count: sales.filter(s => s.source === 'delivery_app').length, total: sales.filter(s => s.source === 'delivery_app').reduce((sum, s) => sum + s.total, 0) },
    { source: 'online', count: sales.filter(s => s.source === 'online').length, total: sales.filter(s => s.source === 'online').reduce((sum, s) => sum + s.total, 0) },
    { source: 'phone', count: sales.filter(s => s.source === 'phone').length, total: sales.filter(s => s.source === 'phone').reduce((sum, s) => sum + s.total, 0) },
  ].filter(item => item.count > 0)

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">إدخال المبيعات</h1>
          <p className="text-muted-foreground">
            تسجيل وإدارة جميع المبيعات من مختلف المصادر
          </p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          مبيعة جديدة
        </Button>
      </div>

      {/* بطاقات الإحصائيات */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المبيعات</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalSales)}</div>
            <p className="text-xs text-muted-foreground">هذا الشهر</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">مبيعات مكتملة</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalCompleted)}</div>
            <p className="text-xs text-muted-foreground">{completedSales.length} معاملة</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">مبيعات معلقة</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingCount}</div>
            <p className="text-xs text-muted-foreground">تحتاج متابعة</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">متوسط المبيعة</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(averageSale)}</div>
            <p className="text-xs text-muted-foreground">للمعاملة الواحدة</p>
          </CardContent>
        </Card>
      </div>

      {/* المبيعات حسب المصدر */}
      <Card>
        <CardHeader>
          <CardTitle>المبيعات حسب المصدر</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {salesBySource.map((item) => (
              <div key={item.source} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  {getSourceIcon(item.source as Sale['source'])}
                  <div>
                    <p className="font-medium">{getSourceText(item.source as Sale['source'])}</p>
                    <p className="text-sm text-muted-foreground">{item.count} معاملة</p>
                  </div>
                </div>
                <div className="text-left">
                  <p className="font-bold">{formatCurrency(item.total)}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* أدوات البحث والتصفية */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>قائمة المبيعات</CardTitle>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="البحث في المبيعات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="h-10 w-80 rounded-lg border border-input bg-background pr-10 pl-4 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                />
              </div>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-right p-4 font-medium">رقم الفاتورة</th>
                  <th className="text-right p-4 font-medium">العميل</th>
                  <th className="text-right p-4 font-medium">التاريخ</th>
                  <th className="text-right p-4 font-medium">المبلغ الإجمالي</th>
                  <th className="text-right p-4 font-medium">طريقة الدفع</th>
                  <th className="text-right p-4 font-medium">المصدر</th>
                  <th className="text-right p-4 font-medium">الحالة</th>
                  <th className="text-right p-4 font-medium">موظف المبيعات</th>
                  <th className="text-right p-4 font-medium">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredSales.map((sale) => (
                  <tr key={sale.id} className="border-b hover:bg-muted/50">
                    <td className="p-4 font-medium">{sale.invoiceNumber}</td>
                    <td className="p-4">
                      <div>
                        <p className="font-medium">{sale.customerName}</p>
                        {sale.customerPhone && (
                          <p className="text-sm text-muted-foreground">{sale.customerPhone}</p>
                        )}
                      </div>
                    </td>
                    <td className="p-4">{formatDate(sale.date)}</td>
                    <td className="p-4 font-medium">{formatCurrency(sale.total)}</td>
                    <td className="p-4">{getPaymentMethodText(sale.paymentMethod)}</td>
                    <td className="p-4">
                      <div className="flex items-center gap-2">
                        {getSourceIcon(sale.source)}
                        <span>{getSourceText(sale.source)}</span>
                        {sale.deliveryApp && (
                          <span className="text-xs text-muted-foreground">({sale.deliveryApp})</span>
                        )}
                      </div>
                    </td>
                    <td className="p-4">
                      <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(sale.status)}`}>
                        {getStatusText(sale.status)}
                      </span>
                    </td>
                    <td className="p-4">{sale.salesPerson}</td>
                    <td className="p-4">
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
