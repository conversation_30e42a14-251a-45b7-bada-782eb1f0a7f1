"use client"

import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { formatCurrency, formatDate } from '@/lib/utils'
import {
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  CreditCard,
  Calendar,
  TrendingDown,
  AlertCircle,
} from 'lucide-react'

interface Payment {
  id: string
  recipient: string
  amount: number
  date: Date
  dueDate: Date
  method: 'cash' | 'bank' | 'check' | 'card'
  status: 'paid' | 'pending' | 'overdue' | 'cancelled'
  category: string
  description: string
  reference?: string
}

const mockPayments: Payment[] = [
  {
    id: '1',
    recipient: 'شركة التوريد المحدودة',
    amount: 15000,
    date: new Date('2024-01-15'),
    dueDate: new Date('2024-01-20'),
    method: 'bank',
    status: 'paid',
    category: 'موردين',
    description: 'دفعة مستحقات شهر ديسمبر',
    reference: 'TXN-001',
  },
  {
    id: '2',
    recipient: 'شركة الكهرباء',
    amount: 2500,
    date: new Date('2024-01-14'),
    dueDate: new Date('2024-01-25'),
    method: 'bank',
    status: 'pending',
    category: 'مرافق',
    description: 'فاتورة الكهرباء - يناير',
  },
  {
    id: '3',
    recipient: 'أحمد محمد - موظف',
    amount: 8000,
    date: new Date('2024-01-13'),
    dueDate: new Date('2024-01-15'),
    method: 'cash',
    status: 'overdue',
    category: 'رواتب',
    description: 'راتب شهر ديسمبر',
  },
]

function getStatusColor(status: Payment['status']) {
  switch (status) {
    case 'paid':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
    case 'overdue':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
    case 'cancelled':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

function getStatusText(status: Payment['status']) {
  switch (status) {
    case 'paid':
      return 'مدفوع'
    case 'pending':
      return 'معلق'
    case 'overdue':
      return 'متأخر'
    case 'cancelled':
      return 'ملغي'
    default:
      return 'غير محدد'
  }
}

function getMethodText(method: Payment['method']) {
  switch (method) {
    case 'cash':
      return 'نقدي'
    case 'bank':
      return 'تحويل بنكي'
    case 'check':
      return 'شيك'
    case 'card':
      return 'بطاقة'
    default:
      return 'غير محدد'
  }
}

export function PaymentsModule() {
  const [payments] = useState<Payment[]>(mockPayments)
  const [searchTerm, setSearchTerm] = useState('')

  const filteredPayments = payments.filter(payment =>
    payment.recipient.toLowerCase().includes(searchTerm.toLowerCase()) ||
    payment.category.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const totalPaid = payments.filter(p => p.status === 'paid').reduce((sum, p) => sum + p.amount, 0)
  const totalPending = payments.filter(p => p.status === 'pending').reduce((sum, p) => sum + p.amount, 0)
  const overdueCount = payments.filter(p => p.status === 'overdue').length

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">إدارة المدفوعات</h1>
          <p className="text-muted-foreground">
            تتبع وإدارة جميع المدفوعات والالتزامات المالية
          </p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          دفعة جديدة
        </Button>
      </div>

      {/* بطاقات الإحصائيات */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المدفوع</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalPaid)}</div>
            <p className="text-xs text-muted-foreground">هذا الشهر</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">مدفوعات معلقة</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalPending)}</div>
            <p className="text-xs text-muted-foreground">في الانتظار</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">مدفوعات متأخرة</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{overdueCount}</div>
            <p className="text-xs text-muted-foreground">تحتاج متابعة</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">متوسط الدفعة</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(payments.length > 0 ? totalPaid / payments.filter(p => p.status === 'paid').length : 0)}
            </div>
            <p className="text-xs text-muted-foreground">للدفعة الواحدة</p>
          </CardContent>
        </Card>
      </div>

      {/* أدوات البحث والتصفية */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>قائمة المدفوعات</CardTitle>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="البحث في المدفوعات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="h-10 w-80 rounded-lg border border-input bg-background pr-10 pl-4 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                />
              </div>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-right p-4 font-medium">المستفيد</th>
                  <th className="text-right p-4 font-medium">المبلغ</th>
                  <th className="text-right p-4 font-medium">تاريخ الدفع</th>
                  <th className="text-right p-4 font-medium">تاريخ الاستحقاق</th>
                  <th className="text-right p-4 font-medium">طريقة الدفع</th>
                  <th className="text-right p-4 font-medium">الفئة</th>
                  <th className="text-right p-4 font-medium">الحالة</th>
                  <th className="text-right p-4 font-medium">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredPayments.map((payment) => (
                  <tr key={payment.id} className="border-b hover:bg-muted/50">
                    <td className="p-4 font-medium">{payment.recipient}</td>
                    <td className="p-4 font-medium">{formatCurrency(payment.amount)}</td>
                    <td className="p-4">{formatDate(payment.date)}</td>
                    <td className="p-4">{formatDate(payment.dueDate)}</td>
                    <td className="p-4">{getMethodText(payment.method)}</td>
                    <td className="p-4">{payment.category}</td>
                    <td className="p-4">
                      <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(payment.status)}`}>
                        {getStatusText(payment.status)}
                      </span>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
