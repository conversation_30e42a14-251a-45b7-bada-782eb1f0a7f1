export default function PaymentModule() {
  return (
    <div className="payment-module">
      <div className="payment-overview">
        <Card className="payment-summary">
          <h3>ملخص المدفوعات</h3>
          <div className="payment-stats">
            <div>المدفوعات اليوم: 25,000 ر.س</div>
            <div>المستحقة: 15,000 ر.س</div>
            <div>المتأخرة: 5,000 ر.س</div>
          </div>
        </Card>
      </div>

      <div className="payment-calendar">
        <Calendar 
          events={paymentDueDates}
          onDateClick={handleDateClick}
        />
      </div>

      <PaymentTable 
        payments={payments}
        onPaymentUpdate={handlePaymentUpdate}
      />
    </div>
  );
}