"use client"

import React, { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { formatCurrency } from '@/lib/utils'
import {
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Building2,
  Phone,
  Mail,
  MapPin,
  Star,
} from 'lucide-react'

interface Supplier {
  id: string
  name: string
  contactPerson: string
  email: string
  phone: string
  address: string
  city: string
  country: string
  totalPurchases: number
  outstandingBalance: number
  rating: number
  status: 'active' | 'inactive' | 'blocked'
  category: string
  paymentTerms: string
  lastOrderDate: Date
}

const mockSuppliers: Supplier[] = [
  {
    id: '1',
    name: 'شركة التوريد المحدودة',
    contactPerson: 'أحمد محمد',
    email: '<EMAIL>',
    phone: '+966501234567',
    address: 'شارع الملك فهد، حي العليا',
    city: 'الرياض',
    country: 'السعودية',
    totalPurchases: 150000,
    outstandingBalance: 25000,
    rating: 4.5,
    status: 'active',
    category: 'مواد خام',
    paymentTerms: '30 يوم',
    lastOrderDate: new Date('2024-01-15'),
  },
  {
    id: '2',
    name: 'مؤسسة الجودة التجارية',
    contactPerson: 'فاطمة أحمد',
    email: '<EMAIL>',
    phone: '+966507654321',
    address: 'طريق الأمير محمد بن عبدالعزيز',
    city: 'جدة',
    country: 'السعودية',
    totalPurchases: 85000,
    outstandingBalance: 12000,
    rating: 4.2,
    status: 'active',
    category: 'معدات مكتبية',
    paymentTerms: '15 يوم',
    lastOrderDate: new Date('2024-01-14'),
  },
  {
    id: '3',
    name: 'شركة التقنية المتقدمة',
    contactPerson: 'محمد علي',
    email: '<EMAIL>',
    phone: '+966509876543',
    address: 'شارع التحلية، حي السلامة',
    city: 'الدمام',
    country: 'السعودية',
    totalPurchases: 220000,
    outstandingBalance: 0,
    rating: 4.8,
    status: 'active',
    category: 'تقنية',
    paymentTerms: 'فوري',
    lastOrderDate: new Date('2024-01-13'),
  },
]

function getStatusColor(status: Supplier['status']) {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    case 'inactive':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
    case 'blocked':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

function getStatusText(status: Supplier['status']) {
  switch (status) {
    case 'active':
      return 'نشط'
    case 'inactive':
      return 'غير نشط'
    case 'blocked':
      return 'محظور'
    default:
      return 'غير محدد'
  }
}

function StarRating({ rating }: { rating: number }) {
  return (
    <div className="flex items-center gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`h-4 w-4 ${
            star <= rating
              ? 'fill-yellow-400 text-yellow-400'
              : 'text-gray-300'
          }`}
        />
      ))}
      <span className="text-sm text-muted-foreground mr-1">({rating})</span>
    </div>
  )
}

export function SuppliersModule() {
  const [suppliers] = useState<Supplier[]>(mockSuppliers)
  const [searchTerm, setSearchTerm] = useState('')

  const filteredSuppliers = suppliers.filter(supplier =>
    supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.category.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const totalSuppliers = suppliers.length
  const activeSuppliers = suppliers.filter(s => s.status === 'active').length
  const totalOutstanding = suppliers.reduce((sum, s) => sum + s.outstandingBalance, 0)
  const totalPurchases = suppliers.reduce((sum, s) => sum + s.totalPurchases, 0)

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">إدارة الموردين</h1>
          <p className="text-muted-foreground">
            إدارة معلومات الموردين وتتبع المعاملات التجارية
          </p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          مورد جديد
        </Button>
      </div>

      {/* بطاقات الإحصائيات */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الموردين</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalSuppliers}</div>
            <p className="text-xs text-muted-foreground">{activeSuppliers} نشط</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المشتريات</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalPurchases)}</div>
            <p className="text-xs text-muted-foreground">جميع الموردين</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">أرصدة مستحقة</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalOutstanding)}</div>
            <p className="text-xs text-muted-foreground">مبالغ مستحقة</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">متوسط التقييم</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(suppliers.reduce((sum, s) => sum + s.rating, 0) / suppliers.length).toFixed(1)}
            </div>
            <p className="text-xs text-muted-foreground">من 5 نجوم</p>
          </CardContent>
        </Card>
      </div>

      {/* أدوات البحث والتصفية */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>قائمة الموردين</CardTitle>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="البحث في الموردين..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="h-10 w-80 rounded-lg border border-input bg-background pr-10 pl-4 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                />
              </div>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredSuppliers.map((supplier) => (
              <Card key={supplier.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{supplier.name}</CardTitle>
                      <p className="text-sm text-muted-foreground">{supplier.category}</p>
                    </div>
                    <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(supplier.status)}`}>
                      {getStatusText(supplier.status)}
                    </span>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{supplier.phone}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span>{supplier.email}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span>{supplier.city}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">إجمالي المشتريات:</span>
                      <span className="font-medium">{formatCurrency(supplier.totalPurchases)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">رصيد مستحق:</span>
                      <span className="font-medium">{formatCurrency(supplier.outstandingBalance)}</span>
                    </div>
                  </div>

                  <StarRating rating={supplier.rating} />

                  <div className="flex items-center gap-2 pt-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="h-4 w-4 ml-1" />
                      عرض
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <Edit className="h-4 w-4 ml-1" />
                      تعديل
                    </Button>
                    <Button variant="outline" size="sm">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
