import { Card, Grid, Switch, Select } from '@mui/material';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'recharts';

export default function Dashboard() {
  return (
    <div className="dashboard-container">
      {/* Header with KPIs */}
      <Grid container spacing={3} className="kpi-section">
        <Grid item xs={12} md={3}>
          <Card className="kpi-card revenue">
            <h3>إجمالي الإيرادات</h3>
            <span className="amount">125,000 ر.س</span>
            <span className="trend positive">+12%</span>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card className="kpi-card expenses">
            <h3>إجمالي المصروفات</h3>
            <span className="amount">85,000 ر.س</span>
            <span className="trend negative">-5%</span>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card className="kpi-card profit">
            <h3>صافي الربح</h3>
            <span className="amount">40,000 ر.س</span>
            <span className="trend positive">+18%</span>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card className="kpi-card pending">
            <h3>المدفوعات المعلقة</h3>
            <span className="amount">15,000 ر.س</span>
            <span className="count">12 فاتورة</span>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} className="charts-section">
        <Grid item xs={12} md={8}>
          <Card className="chart-card">
            <h3>تحليل الإيرادات والمصروفات</h3>
            <LineChart width={600} height={300} data={revenueData}>
              {/* Chart components */}
            </LineChart>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card className="chart-card">
            <h3>توزيع المصروفات</h3>
            <PieChart width={300} height={300} data={expenseData}>
              {/* Pie chart components */}
            </PieChart>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Actions & Activity Feed */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card className="quick-actions">
            <h3>إجراءات سريعة</h3>
            <button className="action-btn">إضافة مبيعات</button>
            <button className="action-btn">تسجيل مصروف</button>
            <button className="action-btn">إنشاء فاتورة</button>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card className="activity-feed">
            <h3>النشاطات الأخيرة</h3>
            <div className="activity-item">
              <span>تم إضافة فاتورة جديدة - 5,000 ر.س</span>
              <time>منذ ساعتين</time>
            </div>
          </Card>
        </Grid>
      </Grid>
    </div>
  );
}