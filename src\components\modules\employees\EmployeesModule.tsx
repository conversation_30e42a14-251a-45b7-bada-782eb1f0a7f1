"use client"

import React, { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { formatCurrency, formatDate } from '@/lib/utils'
import {
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Users,
  UserCheck,
  Calendar,
  DollarSign,
  Phone,
  Mail,
  MapPin,
} from 'lucide-react'

interface Employee {
  id: string
  name: string
  position: string
  department: string
  email: string
  phone: string
  address: string
  hireDate: Date
  salary: number
  status: 'active' | 'inactive' | 'on_leave'
  employeeId: string
  nationalId: string
  bankAccount: string
  emergencyContact: string
  lastPayment: Date
}

const mockEmployees: Employee[] = [
  {
    id: '1',
    name: 'أحمد محمد علي',
    position: 'مدير المبيعات',
    department: 'المبيعات',
    email: '<EMAIL>',
    phone: '+************',
    address: 'الرياض، حي العليا',
    hireDate: new Date('2022-03-15'),
    salary: 12000,
    status: 'active',
    employeeId: 'EMP-001',
    nationalId: '**********',
    bankAccount: '************************',
    emergencyContact: '+************',
    lastPayment: new Date('2024-01-01'),
  },
  {
    id: '2',
    name: 'فاطمة أحمد السالم',
    position: 'محاسبة',
    department: 'المالية',
    email: '<EMAIL>',
    phone: '+************',
    address: 'جدة، حي الزهراء',
    hireDate: new Date('2021-08-20'),
    salary: 9000,
    status: 'active',
    employeeId: 'EMP-002',
    nationalId: '**********',
    bankAccount: '************************',
    emergencyContact: '+************',
    lastPayment: new Date('2024-01-01'),
  },
  {
    id: '3',
    name: 'محمد علي الأحمد',
    position: 'مطور برمجيات',
    department: 'التقنية',
    email: '<EMAIL>',
    phone: '+************',
    address: 'الدمام، حي الفيصلية',
    hireDate: new Date('2023-01-10'),
    salary: 15000,
    status: 'on_leave',
    employeeId: 'EMP-003',
    nationalId: '**********',
    bankAccount: '************************',
    emergencyContact: '+************',
    lastPayment: new Date('2024-01-01'),
  },
]

function getStatusColor(status: Employee['status']) {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    case 'inactive':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    case 'on_leave':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

function getStatusText(status: Employee['status']) {
  switch (status) {
    case 'active':
      return 'نشط'
    case 'inactive':
      return 'غير نشط'
    case 'on_leave':
      return 'في إجازة'
    default:
      return 'غير محدد'
  }
}

export function EmployeesModule() {
  const [employees] = useState<Employee[]>(mockEmployees)
  const [searchTerm, setSearchTerm] = useState('')

  const filteredEmployees = employees.filter(employee =>
    employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.department.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const totalEmployees = employees.length
  const activeEmployees = employees.filter(e => e.status === 'active').length
  const totalSalaries = employees.reduce((sum, e) => sum + e.salary, 0)
  const onLeaveCount = employees.filter(e => e.status === 'on_leave').length

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">إدارة الموظفين</h1>
          <p className="text-muted-foreground">
            إدارة معلومات الموظفين والرواتب والحضور
          </p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          موظف جديد
        </Button>
      </div>

      {/* بطاقات الإحصائيات */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الموظفين</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalEmployees}</div>
            <p className="text-xs text-muted-foreground">{activeEmployees} نشط</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي الرواتب</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalSalaries)}</div>
            <p className="text-xs text-muted-foreground">شهرياً</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">موظفين في إجازة</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{onLeaveCount}</div>
            <p className="text-xs text-muted-foreground">حالياً</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">متوسط الراتب</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(totalEmployees > 0 ? totalSalaries / totalEmployees : 0)}
            </div>
            <p className="text-xs text-muted-foreground">للموظف الواحد</p>
          </CardContent>
        </Card>
      </div>

      {/* أدوات البحث والتصفية */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>قائمة الموظفين</CardTitle>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="البحث في الموظفين..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="h-10 w-80 rounded-lg border border-input bg-background pr-10 pl-4 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                />
              </div>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredEmployees.map((employee) => (
              <Card key={employee.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{employee.name}</CardTitle>
                      <p className="text-sm text-muted-foreground">{employee.position}</p>
                      <p className="text-xs text-muted-foreground">{employee.department}</p>
                    </div>
                    <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(employee.status)}`}>
                      {getStatusText(employee.status)}
                    </span>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{employee.phone}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span className="truncate">{employee.email}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span>{employee.address}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">رقم الموظف:</span>
                      <span className="font-medium">{employee.employeeId}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">الراتب:</span>
                      <span className="font-medium">{formatCurrency(employee.salary)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">تاريخ التوظيف:</span>
                      <span className="font-medium">{formatDate(employee.hireDate)}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 pt-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="h-4 w-4 ml-1" />
                      عرض
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <Edit className="h-4 w-4 ml-1" />
                      تعديل
                    </Button>
                    <Button variant="outline" size="sm">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
