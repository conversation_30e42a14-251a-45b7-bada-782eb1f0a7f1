export default function PurchaseModule() {
  return (
    <div className="purchase-module">
      <div className="module-header">
        <h2>إدارة المشتريات</h2>
        <button className="add-btn">إضافة طلب شراء</button>
      </div>
      
      <div className="purchase-tabs">
        <Tab label="طلبات الشراء" />
        <Tab label="فواتير الموردين" />
        <Tab label="الموافقات المعلقة" />
        <Tab label="المخزون الوارد" />
      </div>

      <DataTable 
        columns={purchaseColumns}
        data={purchases}
        filters={['supplier', 'status', 'date']}
        actions={['edit', 'approve', 'cancel']}
      />
    </div>
  );
}