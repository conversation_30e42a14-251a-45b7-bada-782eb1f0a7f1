import type { Metadata } from 'next'
import './globals.css'
import { ThemeProvider } from '@/components/providers/theme-provider'



export const metadata: Metadata = {
  title: 'نظام المحاسبة الشامل',
  description: 'تطبيق محاسبة متكامل قابل للتخصيص لجميع المجالات التجارية',
  keywords: ['محاسبة', 'إدارة مالية', 'مبيعات', 'مشتريات', 'موظفين'],
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning>
      <body className="font-arabic">
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}
