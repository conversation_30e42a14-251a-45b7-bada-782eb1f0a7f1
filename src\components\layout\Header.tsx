"use client"

import React from 'react'
import { useTheme } from '@/components/providers/theme-provider'
import { Button } from '@/components/ui/button'
import {
  Sun,
  Moon,
  Bell,
  Search,
  Globe,
  Settings,
} from 'lucide-react'

export function Header() {
  const { theme, setTheme } = useTheme()

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark')
  }

  return (
    <header className="flex h-16 items-center justify-between border-b border-border bg-background px-6">
      {/* شريط البحث */}
      <div className="flex items-center gap-4">
        <div className="relative">
          <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <input
            type="text"
            placeholder="البحث في النظام..."
            className="h-10 w-80 rounded-lg border border-input bg-background pr-10 pl-4 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
          />
        </div>
      </div>

      {/* أدوات الهيدر */}
      <div className="flex items-center gap-2">
        {/* تبديل اللغة */}
        <Button variant="ghost" size="icon">
          <Globe className="h-5 w-5" />
        </Button>

        {/* الإشعارات */}
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-xs text-white">
            3
          </span>
        </Button>

        {/* تبديل الثيم */}
        <Button variant="ghost" size="icon" onClick={toggleTheme}>
          {theme === 'dark' ? (
            <Sun className="h-5 w-5" />
          ) : (
            <Moon className="h-5 w-5" />
          )}
        </Button>

        {/* الإعدادات */}
        <Button variant="ghost" size="icon">
          <Settings className="h-5 w-5" />
        </Button>

        {/* معلومات سريعة */}
        <div className="mr-4 text-left">
          <p className="text-sm font-medium">اليوم: {new Date().toLocaleDateString('ar-SA')}</p>
          <p className="text-xs text-muted-foreground">
            آخر تحديث: {new Date().toLocaleTimeString('ar-SA')}
          </p>
        </div>
      </div>
    </header>
  )
}
