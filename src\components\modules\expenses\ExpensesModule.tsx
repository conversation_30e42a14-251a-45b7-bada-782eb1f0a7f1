"use client"

import React, { useState } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { formatCurrency, formatDate } from '@/lib/utils'
import {
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Wallet,
  Calendar,
  TrendingDown,
  Receipt,
  Tag,
} from 'lucide-react'

interface Expense {
  id: string
  description: string
  amount: number
  date: Date
  category: string
  paymentMethod: 'cash' | 'bank' | 'card' | 'check'
  status: 'approved' | 'pending' | 'rejected'
  receipt?: string
  notes?: string
  addedBy: string
  recurring: boolean
  tags: string[]
}

const mockExpenses: Expense[] = [
  {
    id: '1',
    description: 'فاتورة الكهرباء - يناير',
    amount: 2500,
    date: new Date('2024-01-15'),
    category: 'مرافق',
    paymentMethod: 'bank',
    status: 'approved',
    receipt: 'REC-001.pdf',
    notes: 'فاتورة شهرية',
    addedBy: 'أحمد محمد',
    recurring: true,
    tags: ['مرافق', 'شهري'],
  },
  {
    id: '2',
    description: 'وقود السيارات',
    amount: 800,
    date: new Date('2024-01-14'),
    category: 'مواصلات',
    paymentMethod: 'cash',
    status: 'approved',
    addedBy: 'فاطمة أحمد',
    recurring: false,
    tags: ['وقود', 'مواصلات'],
  },
  {
    id: '3',
    description: 'مواد تنظيف المكتب',
    amount: 350,
    date: new Date('2024-01-13'),
    category: 'مكتب',
    paymentMethod: 'cash',
    status: 'pending',
    addedBy: 'محمد علي',
    recurring: false,
    tags: ['تنظيف', 'مكتب'],
  },
  {
    id: '4',
    description: 'اشتراك الإنترنت',
    amount: 500,
    date: new Date('2024-01-12'),
    category: 'تقنية',
    paymentMethod: 'bank',
    status: 'approved',
    recurring: true,
    addedBy: 'أحمد محمد',
    tags: ['إنترنت', 'شهري'],
  },
]

const expenseCategories = [
  'مرافق',
  'مواصلات',
  'مكتب',
  'تقنية',
  'تسويق',
  'صيانة',
  'أخرى',
]

function getStatusColor(status: Expense['status']) {
  switch (status) {
    case 'approved':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
    case 'rejected':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
  }
}

function getStatusText(status: Expense['status']) {
  switch (status) {
    case 'approved':
      return 'معتمد'
    case 'pending':
      return 'في الانتظار'
    case 'rejected':
      return 'مرفوض'
    default:
      return 'غير محدد'
  }
}

function getPaymentMethodText(method: Expense['paymentMethod']) {
  switch (method) {
    case 'cash':
      return 'نقدي'
    case 'bank':
      return 'تحويل بنكي'
    case 'card':
      return 'بطاقة'
    case 'check':
      return 'شيك'
    default:
      return 'غير محدد'
  }
}

export function ExpensesModule() {
  const [expenses] = useState<Expense[]>(mockExpenses)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  const filteredExpenses = expenses.filter(expense => {
    const matchesSearch = expense.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         expense.category.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || expense.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0)
  const approvedExpenses = expenses.filter(e => e.status === 'approved').reduce((sum, e) => sum + e.amount, 0)
  const pendingCount = expenses.filter(e => e.status === 'pending').length
  const recurringExpenses = expenses.filter(e => e.recurring).reduce((sum, e) => sum + e.amount, 0)

  // تجميع المصروفات حسب الفئة
  const expensesByCategory = expenseCategories.map(category => ({
    category,
    amount: expenses.filter(e => e.category === category).reduce((sum, e) => sum + e.amount, 0),
    count: expenses.filter(e => e.category === category).length,
  })).filter(item => item.count > 0)

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">المصروفات اليومية</h1>
          <p className="text-muted-foreground">
            تتبع وإدارة جميع المصروفات اليومية والتشغيلية
          </p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          مصروف جديد
        </Button>
      </div>

      {/* بطاقات الإحصائيات */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المصروفات</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalExpenses)}</div>
            <p className="text-xs text-muted-foreground">هذا الشهر</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">مصروفات معتمدة</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(approvedExpenses)}</div>
            <p className="text-xs text-muted-foreground">تم اعتمادها</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">مصروفات معلقة</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingCount}</div>
            <p className="text-xs text-muted-foreground">تحتاج موافقة</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">مصروفات دورية</CardTitle>
            <Receipt className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(recurringExpenses)}</div>
            <p className="text-xs text-muted-foreground">شهرياً</p>
          </CardContent>
        </Card>
      </div>

      {/* المصروفات حسب الفئة */}
      <Card>
        <CardHeader>
          <CardTitle>المصروفات حسب الفئة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {expensesByCategory.map((item) => (
              <div key={item.category} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">{item.category}</p>
                  <p className="text-sm text-muted-foreground">{item.count} مصروف</p>
                </div>
                <div className="text-left">
                  <p className="font-bold">{formatCurrency(item.amount)}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* أدوات البحث والتصفية */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>قائمة المصروفات</CardTitle>
            <div className="flex items-center gap-2">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="h-10 rounded-lg border border-input bg-background px-3 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
              >
                <option value="all">جميع الفئات</option>
                {expenseCategories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
              <div className="relative">
                <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="البحث في المصروفات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="h-10 w-80 rounded-lg border border-input bg-background pr-10 pl-4 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                />
              </div>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-right p-4 font-medium">الوصف</th>
                  <th className="text-right p-4 font-medium">المبلغ</th>
                  <th className="text-right p-4 font-medium">التاريخ</th>
                  <th className="text-right p-4 font-medium">الفئة</th>
                  <th className="text-right p-4 font-medium">طريقة الدفع</th>
                  <th className="text-right p-4 font-medium">الحالة</th>
                  <th className="text-right p-4 font-medium">مضاف بواسطة</th>
                  <th className="text-right p-4 font-medium">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredExpenses.map((expense) => (
                  <tr key={expense.id} className="border-b hover:bg-muted/50">
                    <td className="p-4">
                      <div>
                        <p className="font-medium">{expense.description}</p>
                        {expense.tags.length > 0 && (
                          <div className="flex gap-1 mt-1">
                            {expense.tags.map((tag, index) => (
                              <span key={index} className="inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs bg-muted">
                                <Tag className="h-3 w-3" />
                                {tag}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="p-4 font-medium">{formatCurrency(expense.amount)}</td>
                    <td className="p-4">{formatDate(expense.date)}</td>
                    <td className="p-4">{expense.category}</td>
                    <td className="p-4">{getPaymentMethodText(expense.paymentMethod)}</td>
                    <td className="p-4">
                      <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(expense.status)}`}>
                        {getStatusText(expense.status)}
                      </span>
                    </td>
                    <td className="p-4">{expense.addedBy}</td>
                    <td className="p-4">
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
