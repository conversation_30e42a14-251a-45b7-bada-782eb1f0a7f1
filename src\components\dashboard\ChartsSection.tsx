"use client"

import React from 'react'
import { Card, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'

// بيانات وهمية للرسوم البيانية
const revenueData = [
  { month: 'يناير', revenue: 45000, expenses: 32000 },
  { month: 'فبراير', revenue: 52000, expenses: 35000 },
  { month: 'مارس', revenue: 48000, expenses: 33000 },
  { month: 'أبريل', revenue: 61000, expenses: 38000 },
  { month: 'مايو', revenue: 55000, expenses: 36000 },
  { month: 'يونيو', revenue: 67000, expenses: 41000 },
]

const salesData = [
  { day: 'السبت', sales: 120 },
  { day: 'الأحد', sales: 98 },
  { day: 'الاثنين', sales: 145 },
  { day: 'الثلاثاء', sales: 132 },
  { day: 'الأربعاء', sales: 156 },
  { day: 'الخميس', sales: 189 },
  { day: 'الجمعة', sales: 167 },
]

const categoryData = [
  { name: 'إلكترونيات', value: 35, color: '#3b82f6' },
  { name: 'ملابس', value: 25, color: '#10b981' },
  { name: 'طعام', value: 20, color: '#f59e0b' },
  { name: 'كتب', value: 12, color: '#ef4444' },
  { name: 'أخرى', value: 8, color: '#8b5cf6' },
]

export function ChartsSection() {
  return (
    <div className="grid gap-6 md:grid-cols-2">
      {/* رسم بياني للإيرادات والمصروفات */}
      <Card className="col-span-2">
        <CardHeader>
          <CardTitle>الإيرادات والمصروفات الشهرية</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-center">
              <div className="text-4xl mb-4">📊</div>
              <p className="text-lg font-medium">رسم بياني للإيرادات والمصروفات</p>
              <p className="text-sm text-muted-foreground mt-2">
                سيتم عرض الرسوم البيانية التفاعلية هنا
              </p>
              <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded"></div>
                  <span>الإيرادات: {revenueData[revenueData.length - 1]?.revenue.toLocaleString('ar-SA')} ريال</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded"></div>
                  <span>المصروفات: {revenueData[revenueData.length - 1]?.expenses.toLocaleString('ar-SA')} ريال</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* رسم بياني للمبيعات اليومية */}
      <Card>
        <CardHeader>
          <CardTitle>المبيعات اليومية</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-center">
              <div className="text-3xl mb-3">📈</div>
              <p className="font-medium">المبيعات اليومية</p>
              <p className="text-sm text-muted-foreground mt-2">
                متوسط المبيعات: {Math.round(salesData.reduce((sum, item) => sum + item.sales, 0) / salesData.length)} معاملة
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* رسم دائري لتوزيع المبيعات حسب الفئة */}
      <Card>
        <CardHeader>
          <CardTitle>توزيع المبيعات حسب الفئة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-center">
              <div className="text-3xl mb-3">🥧</div>
              <p className="font-medium">توزيع المبيعات</p>
              <div className="mt-3 space-y-2 text-sm">
                {categoryData.map((item, index) => (
                  <div key={index} className="flex items-center gap-2 justify-center">
                    <div className="w-3 h-3 rounded" style={{ backgroundColor: item.color }}></div>
                    <span>{item.name}: {item.value}%</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
