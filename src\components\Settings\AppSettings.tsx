export default function AppSettings() {
  return (
    <div className="settings-container">
      <div className="settings-section">
        <h3>الإعدادات العامة</h3>
        
        <div className="setting-item">
          <label>اللغة</label>
          <Select value={language} onChange={handleLanguageChange}>
            <option value="ar">العربية</option>
            <option value="en">English</option>
            <option value="fr">Français</option>
          </Select>
        </div>

        <div className="setting-item">
          <label>الوضع المظلم</label>
          <Switch 
            checked={darkMode} 
            onChange={toggleDarkMode}
          />
        </div>

        <div className="setting-item">
          <label>العملة الأساسية</label>
          <Select value={currency} onChange={handleCurrencyChange}>
            <option value="SAR">ريال سعودي</option>
            <option value="USD">دولار أمريكي</option>
            <option value="EUR">يورو</option>
          </Select>
        </div>

        <div className="setting-item">
          <label>تحويل العملات التلقائي</label>
          <Switch checked={autoConvert} onChange={toggleAutoConvert} />
        </div>
      </div>

      <div className="settings-section">
        <h3>إعدادات الأعمال</h3>
        <BusinessTypeSelector 
          types={['retail', 'restaurant', 'services', 'wholesale']}
          onSelect={handleBusinessTypeChange}
        />
      </div>
    </div>
  );
}